class Jobs::ScheduleLifecycleInvites < BackgroundJob
  has_and_belongs_to_many :participants, class_name: "Person", inverse_of: nil
  field :invalid_participant_ids, type: Array, default: []

  # For exit surveys, the field days_from_onboard_exit represents the maximum number of days prior to an employee’s
  # last day before they should be sent an invitation to complete an exit survey.
  DEFAULT_DAYS_BEFORE_EXIT_TO_TRIGGER_INVITE = 14

  # For onboarding surveys, the field days_from_onboard_exit represents the minimum number of days since an employee’s
  # start date before an invitation to complete an onboarding survey should be sent.
  #
  # -------------------|----------------------------------------------------------------|------------------------|--->
  #         maximum_days_since_start_date                                      days_from_onboard_exit           today
  #         e.g. 120 days since start_date                                  e.g. 90 days since start_date
  #                    |----------------- Invitation window ----------------------------|
  #
  # The maximum is in place so that if there is a significant delay between an employee’s start date and their data
  # being uploaded into the system (e.g. more than 120 days), they will not receive an onboarding survey.
  DEFAULT_MINIMUM_DAYS_SINCE_ONBOARDING_TO_TRIGGER_INVITE = 30
  DEFAULT_MAXIMUM_WINDOW_TO_TRIGGER_INVITE = 30
  field :days_from_onboard_exit, type: Integer, default: -> { survey&.onboard? ? DEFAULT_MINIMUM_DAYS_SINCE_ONBOARDING_TO_TRIGGER_INVITE : DEFAULT_DAYS_BEFORE_EXIT_TO_TRIGGER_INVITE }
  field :maximum_days_since_start_date, type: Integer, default: -> { survey&.onboard? ? days_from_onboard_exit + DEFAULT_MAXIMUM_WINDOW_TO_TRIGGER_INVITE : nil }

  field :status, default: :hold, overwrite: true

  AFFIRMATIVE_ANSWERS = %w[yes true y on 1] # Lower case versions of all possible affirmitive answer types to the
  CLONABLE_ATTRIBUTES = %w[days_from_onboard_exit maximum_days_since_start_date]
  CLIENT_CHURNED_MESSAGE = "Client churned; Postponing delivery."

  def run
    if survey.nil? || survey.deleted?
      unless Survey.deleted.where(_id: survey_id).exists?
        fail "Survey missing! #{survey_id}"
      end
    elsif account.churned?
      postpone! CLIENT_CHURNED_MESSAGE
    elsif survey.status == :active &&
        !survey.archived? &&
        survey.flag(Flags::TRIGGER_LIFECYCLE_INVITES) == Flags::ENABLED &&
        !survey.full_workflow? # double-check TRIGGER_LIFECYCLE_INVITES is allowed, since we don't currently support automatic invitations on full workflow surveys

      self.participants, self.invalid_participant_ids = Jobs::ParticipantsValidator.split_into_valid_people_and_invalid_ids(participants_to_invite)
      save!

      SurveyInviteFailedMailer.delay.generate(account.name, survey.name, id, invalid_participant_ids) unless invalid_participant_ids.empty?

      correlation_id = SecureRandom.uuid
      assign_random_demographic_values(participants)
      Datadog.tracer.trace("SurveyLaunch", {service: "murmur", resource: "Jobs::ScheduleLifecycleInvites"}) do |span|
        span.set_tag("survey.id", survey.id)
        span.set_tag("correlation_id", correlation_id)
        span.set_tag("schedule_job.id", id)
        invite_participants(participants, correlation_id)
      end
      ## reschedule it for the next day
      reschedule(run_at + 1.day) if run_at.present?
    end
  end

  def participant_count
    participants_to_invite.count
  end

  def error_hook
    return if self.class.where(survey: survey).not_started.count > 0

    reschedule(run_at + 1.day) if run_at.present?
  end

  private

  InviteParticipantLifecycleJob = Struct.new(:survey, :person, :correlation_id) {
    def log(**attrs)
      Splunk::Logger.new(Rails.logger).log(
        app: "murmur",
        module: "Jobs::ScheduleLifecycleInvites",
        **attrs
      )
    end

    def invite_participant
      identifier = person.name
      identifier += " (#{person.employee_id})" if person.employee_id.present?
      identifier += " - #{person.email}" if person.email.present?
      log(
        correlation_id: correlation_id,
        message: "Inviting #{identifier} to #{survey.id}"
      )

      survey_topic = SurveyTopic.create(
        send_survey_to: :employee,
        subject: person,
        survey: survey,
        survey_due_date: survey.lifecycle_due_date,
        interview_due_date: survey.lifecycle_interview_due_date
      )
      Surveys::Commands::StartLifecycleProcess.new.call(survey_topic: survey_topic, correlation_id: correlation_id)
      log(
        correlation_id: correlation_id,
        message: "Created lifecycle process for survey topic with subject #{identifier}"
      )
    end

    def max_attempts
      1
    end

    def queue
      "uninterruptible_long"
    end

    def error(job, exception)
      # The splunk log formatter regex capture groups are a little broken, and don't capture the full backtrace, so we will avoid for now
      log(
        correlation_id: correlation_id,
        message: "invite_participant_lifecycle_job - job id #{job.id}: #{exception&.message}\n#{exception&.backtrace&.join("\n")}"
      )
    end
  }

  def invite_participants(participants, correlation_id)
    Jobs::Throttler.for_emails(entries: participants, survey: survey).run do |person, run_at|
      invite_person(person, run_at, correlation_id)
    end
  end

  def invite_person(person, run_at, correlation_id)
    InviteParticipantLifecycleJob.new(survey, person, correlation_id).delay(run_at: run_at).invite_participant
  end

  def participants_to_invite
    # @see #eligible? for an explanation of how TRIGGER_ELIGIBILITY_FLAG works
    eligibility_flag_code = survey.config(Configs::TRIGGER_ELIGIBILITY_FLAG)
    eligilibity_flag = Question.where(code: eligibility_flag_code).first if eligibility_flag_code.present?

    already_invited = participants_with_non_deleted_responses

    employees_in_date_range(survey).select do |person|
      !already_invited.include?(person.id) && eligible?(person, eligilibity_flag)
    end
  end

  def employees_in_date_range(survey)
    if survey.type == :onboard
      Lifecycle::Onboard::EmployeesToInvite.new(
        account: survey.account,
        minimum_period: days_from_onboard_exit,
        maximum_period: maximum_days_since_start_date
      ).call
    elsif survey.type == :exit
      Lifecycle::Exit::EmployeesToInvite.new(
        account: survey.account,
        maximum_period_before_exit: days_from_onboard_exit
      ).call
    end
  end

  # TRIGGER_ELIGIBILITY_FLAG is a survey specific configuration that points to a special purpose demographic
  # in the survey that is used to "label" which employees are eligible for lifecycle surveys.
  # If the employee has any of the  AFFIRMATIVE_ANSWERS as a value in that demographic, then it is considered eligible.
  # The feature was built as a workaround to exclude employees like contractors.
  def eligible?(person, eligilibity_flag)
    return false if person.advisor?
    return true if eligilibity_flag.nil?

    demographic_value_label = person.demographic_value_label(eligilibity_flag.aggregate_id)
    return false if demographic_value_label.blank? # Can't be eligible if they don't have an assignment!

    AFFIRMATIVE_ANSWERS.include?(demographic_value_label.downcase)
  end

  def participants_with_non_deleted_responses
    Response.where(survey_id: survey.id).real.pluck(:user_id)
  end

  # the demographic values are assigned in a cyclic fashion to achieve even distribution
  # the shuffle before the cyclic assignment is to ensure that there is no bias in favour of the first few values
  def assign_random_demographic_values(people)
    random_stq = random_demographic_stq
    return if random_stq.blank?

    shuffled_demographic_values = shuffle_demographic_values(random_stq).cycle
    demographic_id = random_stq.question.aggregate_id

    people.each do |person|
      demographic_value_id = person.demographic_value(demographic_id)
      next if demographic_value_id.present?

      demographic_value_id = shuffled_demographic_values.next
      Rails.logger.info "Setting random demographic_value #{demographic_id} => #{demographic_value_id} for person_id: #{person.id}"
      person.set_demographic_value!(demographic_id, demographic_value_id)
    end
  end

  def shuffle_demographic_values(random_stq)
    random_stq.question.select_options.all.map(&:demographic_value_id).shuffle
  end

  def random_demographic_stq
    return if survey.config(Configs::RANDOM_DEMOGRAPHIC_CODE).blank?

    survey.active_demographics.detect { |stq| stq.question.code == survey.config(Configs::RANDOM_DEMOGRAPHIC_CODE) }
  end
end
