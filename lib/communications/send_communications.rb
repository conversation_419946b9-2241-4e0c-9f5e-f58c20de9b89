require_relative "./slack/communication_content"
require_relative "./slack/send_direct_message"
require_relative "./email/send_email"

module Communications
  class SendCommunications
    def initialize(
      slack_communication_variables:,
      ms_teams_communication_variables:,
      slack_communication_content: Communications::Slack::CommunicationContent.new,
      slack_direct_message: Communications::Slack::SendDirectMessage.new,
      ms_teams_direct_message: MicrosoftTeams::TeamsNotifier.new,
      send_email: Communications::Email::SendEmail.new
    )
      @slack_communication_content = slack_communication_content
      @slack_communication_variables = slack_communication_variables
      @slack_direct_message = slack_direct_message
      @ms_teams_communication_variables = ms_teams_communication_variables
      @ms_teams_direct_message = ms_teams_direct_message
      @send_email = send_email
    end

    def call(configurations:)
      valid_configurations = configurations
        .select(&:send_by_email?)

      valid_configurations.each do |configuration|
        send_via_email(configuration)

        send_via_ms_teams(configuration) if ms_teams_configured?(configuration)
        send_via_slack(configuration) if configuration.send_by_slack?
      end

      valid_configurations.map(&:response)
    end

    private

    def send_via_email(configuration)
      @send_email.call(
        account: configuration.survey.account,
        survey: configuration.survey,
        response: configuration.response,
        recipient: configuration.recipient,
        template_type: configuration.template_type,
        locale: configuration.default_locale,
        additional_fields: configuration.additional_fields,
        conversation: configuration.conversation,
        correlation_id: configuration.correlation_id
      )

      log_amplitude_invite_sent(
        configuration: configuration,
        medium: Analytics::MEDIUM_EMAIL
      )
    end

    def send_via_ms_teams(configuration)
      msteams_content_variables = @ms_teams_communication_variables.to_hash(
        survey: configuration.survey,
        response: configuration.response,
        locale: configuration.default_locale
      )

      @ms_teams_direct_message.call(
        person: configuration.recipient,
        account: configuration.survey.account,
        message: I18n.t("msteams.#{configuration.survey.type}.#{configuration.template_type}", locale: configuration.default_locale) % msteams_content_variables
      )

      log_amplitude_invite_sent(
        configuration: configuration,
        medium: Analytics::MEDIUM_TEAMS
      )
    end

    def ms_teams_configured?(configuration)
      configuration.survey.ms_teams_for_lifecycle_enabled? || configuration.survey.ms_teams_for_effectiveness_enabled?
    end

    def send_via_slack(configuration)
      survey = configuration.survey

      content_variables = @slack_communication_variables.call(
        survey: survey,
        response: configuration.response,
        locale: configuration.default_locale,
        additional_variables: configuration.additional_fields
      )

      msg_content = @slack_communication_content.call(
        survey_id: survey.id,
        survey_type: survey.type,
        template_type: configuration.template_type,
        locale: configuration.default_locale
      )

      @slack_direct_message.call(
        account: survey.account,
        employee: configuration.recipient,
        text: content_variables.call(content: msg_content)
      )

      log_amplitude_invite_sent(
        configuration: configuration,
        medium: Analytics::MEDIUM_SLACK
      )
    end

    def log_amplitude_invite_sent(configuration:, medium:)
      return unless configuration.template_type == "invite_user"

      begin
        Analytics.log_event(
          user_id: configuration.recipient.aggregate_id,
          event_type: "Platform Communication Received",
          event_properties: {
            Analytics::EVENT_PROPERTY_MEDIUM => medium,
            Analytics::EVENT_PROPERTY_COMMUNICATION_NAME => Analytics::COMMUNICATION_NAME_SURVEY_CAPTURE
          },
          group_properties: [
            Analytics::ACCOUNT_GROUP.call(configuration.survey.account.aggregate_id),
            Analytics::SURVEY_GROUP.call(configuration.survey.aggregate_id)
          ]
        )
      rescue
      end
    end
  end
end
