module Communications
  class CommunicationConfiguration
    attr_reader :conversation,
      :response,
      :template_type,
      :recipient,
      :correlation_id,
      :additional_fields

    def initialize(conversation:, response:, template_type:, recipient:, locale: nil, correlation_id: nil, additional_fields: {})
      @conversation = conversation
      @response = response
      @template_type = template_type
      @recipient = recipient
      @locale = locale
      @correlation_id = correlation_id
      @additional_fields = additional_fields
    end

    def ==(other)
      recipient == other.recipient &&
        template_type == other.template_type &&
        response == other.response &&
        conversation == other.conversation
    end

    def send_by_email?
      recipient.email.present? && response.survey.allow_emails?
    end

    def send_by_slack?
      response.survey.slack_ready? && recipient.slackable?
    end

    def survey
      response.survey
    end

    def default_locale
      @locale || recipient.select_preferred_locale(survey.supported_locales)
    end
  end
end
