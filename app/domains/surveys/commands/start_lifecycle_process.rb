module Surveys
  module Commands
    class StartLifecycleProcess
      attr_reader :create_response, :lifecycle_communications

      def initialize(
        create_response: Responses::Commands::CreateResponse.new,
        lifecycle_communications: Lifecycle::LifecycleCommunications.new
      )
        @create_response = create_response
        @lifecycle_communications = lifecycle_communications
      end

      def call(survey_topic:, correlation_id:)
        response = create_response.call(
          user_id: survey_topic.subject.id,
          survey: survey_topic.survey,
          survey_topic: survey_topic
        )

        survey_topic.sort_tag = response.user.name_sort
        survey_topic.responses = [response]
        survey_topic.issue
        survey_topic.save!

        # Invite the interviewer
        if survey_topic.survey.full_workflow? && response.reviewer.present? && !response.reviewer.invited?
          # If the reviewer has never been sent a login invite, send them one now!
          response.reviewer.notify_new_account
        end

        if survey_topic.send_survey_to == :no_one
          # Skip response
          response.update!(status: :submitted)
          survey_topic.update!(status: :submitted)
        end

        # Send invitations
        lifecycle_communications.process_created(survey_topic, correlation_id)
      end
    end
  end
end
