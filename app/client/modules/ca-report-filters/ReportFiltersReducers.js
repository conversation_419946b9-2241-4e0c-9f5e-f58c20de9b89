import { combineReducers } from 'redux';
import Immutable from 'immutable';
import pick from 'lodash/pick';
import { REPORT_DATA_SUCCESS } from 'ca-reports/actions/ReportDataAsyncActions';

import {
  FILTERS_INIT,
  ANCHOR_UPDATE,
  DATE_FILTERS_UPDATE,
  FILTER_CREATE,
  FILTER_UPDATE,
  FILTER_DELETE,
  FILTERS_CLEAR_ALL,
  STATUS_UPDATE,
  SEARCH_TERM_UPDATE,
  SELECTED_LEADER_UPDATE,
} from './ReportFiltersActions';
import {
  FILTERS_APPLY,
  FILTERS_INITIAL_DATA_REQUEST,
  FILTERS_INITIAL_DATA_SUCCESS,
  FILTERS_INITIAL_DATA_FAILURE,
  FILTERS_OPTIONS_UPDATE,
  DEMOGRAPHIC_OPTIONS_DATA_SUCCESS,
  ANCHOR_OPTIONS_UPDATE,
} from './actions/ReportFiltersAsyncActions';
import {
  Anchor,
  DateRangeFilter,
  DemographicFilterRecord,
  Responses,
  Demographic,
  DemographicGroup,
} from './ReportFilterRecords';
import parseDateFromServer from './parseDateFromServer';
import getFiltersFromStoreState from './getFiltersFromStoreState';
import initDemographicFilters from './initDemographicFilters';

function initDateFilters(dateFilters) {
  if (!dateFilters) return null;

  return DateRangeFilter({
    from_date: parseDateFromServer(dateFilters.get('from_date')),
    to_date: parseDateFromServer(dateFilters.get('to_date')),
    max_date: parseDateFromServer(dateFilters.get('max_date')),
    default_from_date: parseDateFromServer(
      dateFilters.get('default_from_date')
    ),
    default_to_date: parseDateFromServer(dateFilters.get('default_to_date')),
  });
}

function initResponses(responses) {
  if (!responses) return null;

  return Responses({
    count: responses.get('count'),
    comment_significant_population: responses.get(
      'comment_significant_population'
    ),
    significant_population: responses.get('significant_population'),
    total_responses: responses.get('total_responses'),
  });
}

function initAnchors(anchors) {
  if (!anchors) {
    return Immutable.List();
  }

  return anchors.map((anchor) =>
    Anchor({
      id: anchor.get('id'),
      text: anchor.get('text'),
      hierarchy: anchor.get('hierarchy'),
    })
  );
}

function initAnchor(anchor) {
  if (!anchor) return null;

  return Anchor(anchor);
}

const makeDemographicOptions = (demographicOptions) => {
  if (!demographicOptions) return null;

  return demographicOptions.map((option) => DemographicGroup(option));
};

function initDemographics(demographics) {
  if (!demographics) return null;

  return demographics.map((demographic) =>
    Demographic({
      id: demographic.get('id'),
      text: demographic.get('text') || demographic.get('name'),
      options: makeDemographicOptions(demographic.get('options')),
      loaded: false,
      filterModule: demographic.get('filterModule'),
    })
  );
}

function initDemographicGroups(demographicGroups) {
  return demographicGroups.map((groups) =>
    groups.map((group) =>
      DemographicGroup({
        id: group.get('id'),
        text: group.get('text'),
        count: group.get('count'),
        enabled: group.get('enabled'),
      })
    )
  );
}

export function getInitialState(serverSentProps) {
  const demographicFilters = initDemographicFilters(
    serverSentProps.demographicFilters
  );

  const dateFilters = initDateFilters(serverSentProps.dateFilters);
  const responses = initResponses(serverSentProps.responses);

  const noDemographics =
    serverSentProps.demographics == null ||
    serverSentProps.demographics.isEmpty();
  let filtersInitialData = null;
  if (serverSentProps.demographicGroups) {
    filtersInitialData = {
      demographicGroups: initDemographicGroups(
        serverSentProps.demographicGroups
      ),
      anchors: [],
    };
  } else if (noDemographics) {
    filtersInitialData = {
      demographicGroups: [],
      anchors: [],
    };
  } // else filtersInitialData will be loaded via async

  const config = pick(serverSentProps, [
    'filtersUrl',
    'filterCountUrl',
    'resultsUrl',
    'statusOptions',
    'maxFilters',
    'uiText',
  ]);

  const demographics = noDemographics
    ? Immutable.List()
    : initDemographics(serverSentProps.demographics);

  const anchor = initAnchor(serverSentProps.anchor);

  const initialState = {
    filtersInitialData: Immutable.fromJS({
      initialData: filtersInitialData,
    }),
    responses,
    demographicFilters,
    demographics,
    dateFilters,
    anchor: anchor,
    anchors: initAnchors(serverSentProps.anchors),
    selectedStatus: serverSentProps.selectedStatus || null,
    searchTerm: serverSentProps.searchTerm || '', // cannot provide null to input fields, must be an empty string or undefined
    dateSummary: Immutable.fromJS(serverSentProps.dateSummary) || null,
    surveyPeriodType: serverSentProps.surveyPeriodType || null,
    selectedLeader: serverSentProps.selectedLeader,
    config,
    removeUnreportableDemographicValuesFromFilterFlag:
      serverSentProps.removeUnreportableDemographicValuesFromFilterFlag,
  };

  initialState.appliedFilters = getFiltersFromStoreState(initialState, true);

  return initialState;
}

const initialStateReducer = (state, action) => {
  switch (action.type) {
    case FILTERS_INIT:
      return getInitialState(action.payload.initialData);
    default:
      return state;
  }
};

function demographicFiltersReducer(state = Immutable.List(), action) {
  switch (action.type) {
    case FILTER_CREATE: {
      const { demographic } = action.payload.filter;

      return state.push(
        DemographicFilterRecord({
          key: demographic.id,
          demographic: action.payload.filter.demographic || null,
          groups: action.payload.filter.groups || Immutable.List(),
          loaded: action.payload.filter.loaded,
        })
      );
    }
    case FILTER_UPDATE:
      return state.set(action.payload.index, action.payload.filter);
    case FILTERS_CLEAR_ALL:
      return state.clear();
    case FILTER_DELETE:
      return state.remove(action.payload.index);
    case FILTERS_OPTIONS_UPDATE: {
      const demographics = action.payload;
      const getDemographicIndex = state.findIndex(
        (filter) => filter.demographic.id === demographics.id
      );

      return state
        .setIn(
          [getDemographicIndex, 'demographic'],
          Demographic({
            id: demographics.id,
            text: demographics.text,
            options: makeDemographicOptions(
              Immutable.fromJS(demographics.options)
            ),
            loaded: true,
            filterModule: demographics.filterModule,
          })
        )
        .setIn([getDemographicIndex, 'loaded'], true);
    }
    default:
      return state;
  }
}

function anchorsReducer(state = null, action) {
  switch (action.type) {
    case ANCHOR_OPTIONS_UPDATE:
      return initAnchors(Immutable.fromJS(action.payload.anchorOptions));
    default:
      return state;
  }
}

function anchorReducer(state = null, action) {
  switch (action.type) {
    case ANCHOR_UPDATE:
      return action.payload.anchor;
    default:
      return state;
  }
}

function statusReducer(state = null, action) {
  switch (action.type) {
    case STATUS_UPDATE:
      return action.payload.status;
    default:
      return state;
  }
}

// ensure that null is not passed into an input field,
// an empty string or undefined is required
function searchTermReducer(state = '', action) {
  switch (action.type) {
    case SEARCH_TERM_UPDATE:
      return action.payload.searchTerm;
    default:
      return state;
  }
}

function dateFiltersReducer(state = Immutable.Map(), action) {
  switch (action.type) {
    case DATE_FILTERS_UPDATE:
      return action.payload.dateFilters;
    case FILTERS_CLEAR_ALL:
      if (state == null) return state;

      return state.merge({
        from_date: state.default_from_date,
        to_date: state.default_to_date,
      });
    default:
      return state;
  }
}

function appliedFiltersReducer(state = null, action) {
  switch (action.type) {
    case FILTERS_APPLY:
      return action.payload.appliedFilters;
    default:
      return state;
  }
}

function filtersInitialDataReducer(state = null, action) {
  switch (action.type) {
    case FILTERS_INITIAL_DATA_REQUEST:
      return state;
    case FILTERS_INITIAL_DATA_SUCCESS: {
      const initialData = Immutable.fromJS({
        demographicGroups: initDemographics(
          Immutable.fromJS(action.payload.initialData.demographicGroups)
        ),
        anchors: initAnchors(
          Immutable.fromJS(action.payload.initialData.anchors)
        ),
      });
      return state.merge({
        initialData: initialData,
      });
    }
    case FILTERS_INITIAL_DATA_FAILURE:
      return state.merge({ error: action.payload });

    default:
      return state;
  }
}

function responsesReducer(state = Immutable.Map(), action) {
  switch (action.type) {
    case REPORT_DATA_SUCCESS:
      if (action.payload.responses) return action.payload.responses;
      return state;
    default:
      return state;
  }
}

function dateSummaryReducer(state = Immutable.Map()) {
  return state;
}

// config is stuff which comes from the server and never changes
// it's convenient for us to keep it in the store for easy access
function configReducer(state = {}) {
  return state;
}

function surveyPeriodTypeReducer(state = {}) {
  return state;
}

function selectedLeaderReducer(state = {}, action) {
  switch (action.type) {
    case SELECTED_LEADER_UPDATE: {
      return state.merge(action.payload.selectedLeader);
    }
    default:
      return state;
  }
}

function demographicsReducer(state = {}, action) {
  switch (action.type) {
    case DEMOGRAPHIC_OPTIONS_DATA_SUCCESS: {
      const demographics = action.payload.demographicGroups[0];
      const getIndex = state.findIndex(
        (demographic) => demographic.id === demographics.id
      );

      return state
        .setIn(
          [getIndex, 'options'],
          makeDemographicOptions(Immutable.fromJS(demographics.options))
        )
        .setIn([getIndex, 'loaded'], true);
    }
    default:
      return state;
  }
}

/*
  each field in the 'ca-report-filters/namespace' has its own reducer
  the reducer returns the type of the value. Since the flag is a boolean
  the state returns a boolean. It is set to false as default as it is
  the default state of the flag.
*/
function removeUnreportableDemographicValuesFromFilterFlagReducer(
  state = false
) {
  return state;
}

const combinedUpdateReducers = combineReducers({
  filtersInitialData: filtersInitialDataReducer,
  demographicFilters: demographicFiltersReducer,
  demographics: demographicsReducer,
  anchor: anchorReducer,
  anchors: anchorsReducer,
  selectedStatus: statusReducer,
  searchTerm: searchTermReducer,
  dateFilters: dateFiltersReducer,
  appliedFilters: appliedFiltersReducer,
  responses: responsesReducer,
  config: configReducer,
  dateSummary: dateSummaryReducer,
  surveyPeriodType: surveyPeriodTypeReducer,
  selectedLeader: selectedLeaderReducer,
  removeUnreportableDemographicValuesFromFilterFlag:
    removeUnreportableDemographicValuesFromFilterFlagReducer,
});

const reportFiltersRootReducer = (state, action) => {
  return combinedUpdateReducers(initialStateReducer(state, action), action);
};

export default reportFiltersRootReducer;
