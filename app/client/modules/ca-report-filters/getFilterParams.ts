import formatFilterQuery from './formatFilterQuery';
import formatDateForServer from './formatDateForServer';
import assertParamValue from 'ca-utils/assert/assertParamValue';
import { getValidDatasetFilters } from 'ca-reports/filters/datasetFilters';
import { IncompleteRootState } from 'ca-report-filters/connectReportFiltersBlock';
import { Anchor } from 'ca-reports/typescriptTypes';
import { List } from 'immutable';

export type FilterParams = {
  filters: string | null;
  a: string | null;
  status: string | null;
  term: string | null;
  from: string | null;
  to: string | null;
  selected_leader: string | null;
};

// build an object of url query params from filters
export default function getFilterParams(
  state: IncompleteRootState
): FilterParams {
  const {
    demographicFilters,
    dateFilters,
    anchor,
    anchors,
    selectedStatus,
    searchTerm,
  } = state;

  const datasetFilters = getValidDatasetFilters(location);

  return {
    filters: assertParamValue(
      demographicFilters ? formatFilterQuery(demographicFilters) : null
    ),
    a: assertParamValue(formatAnchors(anchors || [anchor]) || null),
    selected_leader: datasetFilters.selected_leader
      ? datasetFilters.selected_leader
      : null,
    status: assertParamValue(selectedStatus || null),
    term: assertParamValue(searchTerm || null),
    from: assertParamValue(
      dateFilters ? formatDateForServer(dateFilters.from_date) : null
    ),
    to: assertParamValue(
      dateFilters ? formatDateForServer(dateFilters.to_date) : null
    ),
  };
}

const formatAnchors = (anchors: List<Anchor>): string => {
  return (
    anchors &&
    anchors
      .map((a) => a?.id)
      .filter(Boolean)
      .join(',')
  );
};
