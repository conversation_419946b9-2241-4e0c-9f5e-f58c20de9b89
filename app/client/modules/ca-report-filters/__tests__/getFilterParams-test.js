// ☢️☣️☢️ Warning: automocking enabled in this file. ☢️☣️☢️
// See doc/automocking.md for details on what this means and
// how it might trip you up. If you want to refactor this test
// so it does not depend on automocking, you will be helping
// improve our codebase health!
import { DateRangeFilter } from '../ReportFilterRecords';
import Immutable from 'immutable';

import getFilterParams from '../getFilterParams';

import formatDateForServer from '../formatDateForServer';
import formatFilterQuery from '../formatFilterQuery';

jest.mock('../formatDateForServer');
jest.mock('../formatFilterQuery');

describe('getFilterParams', () => {
  let anchor;
  let anchors;
  let demographicFilters;
  let dateFilters;
  let selectedStatus;
  let searchTerm;

  beforeEach(() => {
    let count = 0;
    formatDateForServer.mockImplementation(() => count++);
    formatFilterQuery.mockImplementation((v) => v);

    demographicFilters = undefined;
    anchor = undefined;
    anchors = undefined;
    dateFilters = undefined;
    selectedStatus = undefined;
    searchTerm = undefined;
  });

  it('it includes empty params when no params provided', () => {
    expect(getFilterParams({})).toEqual({
      filters: null,
      a: null,
      status: null,
      term: null,
      from: null,
      to: null,
      selected_leader: null,
    });
  });

  it('sets the anchor', () => {
    anchor = 'new_anchor';
    expect(getFilterParams({ anchor: { id: anchor } })).toEqual({
      filters: null,
      a: anchor,
      status: null,
      term: null,
      from: null,
      to: null,
      selected_leader: null,
    });
  });

  it('sets the dates', () => {
    dateFilters = DateRangeFilter({
      from_date: Date(0),
      to_date: Date(0),
    });
    expect(getFilterParams({ dateFilters })).toEqual({
      filters: null,
      a: null,
      status: null,
      term: null,
      from: 0,
      to: 1,
      selected_leader: null,
    });
  });

  it('sets the status', () => {
    selectedStatus = 'completed';
    expect(getFilterParams({ selectedStatus })).toEqual({
      filters: null,
      a: null,
      status: selectedStatus,
      term: null,
      from: null,
      to: null,
      selected_leader: null,
    });
  });

  it('sets the term', () => {
    searchTerm = 'halp';
    expect(getFilterParams({ searchTerm })).toEqual({
      filters: null,
      a: null,
      status: null,
      term: searchTerm,
      from: null,
      to: null,
      selected_leader: null,
    });
  });

  it('sets the formatted filters', () => {
    demographicFilters = 'demographicFilters';

    expect(getFilterParams({ demographicFilters })).toEqual({
      filters: demographicFilters,
      a: null,
      status: null,
      term: null,
      from: null,
      to: null,
      selected_leader: null,
    });
  });

  it('sets the selected leader', () => {
    demographicFilters = 'demographicFilters';
    delete global.location;
    window.location = { search: '?selected_leader=123-abc', hash: '' };
    expect(getFilterParams({ demographicFilters })).toEqual({
      filters: demographicFilters,
      a: null,
      status: null,
      term: null,
      from: null,
      to: null,
      selected_leader: '123-abc',
    });
  });

  describe('formatAnchors', () => {
    beforeEach(() => {
      // Reset location to prevent selected_leader from being carried over
      delete global.location;
      window.location = { search: '', hash: '' };
    });

    it('handles a single anchor when anchors is not provided', () => {
      anchor = { id: 'anchor1' };
      expect(getFilterParams({ anchor })).toEqual({
        filters: null,
        a: 'anchor1',
        status: null,
        term: null,
        from: null,
        to: null,
        selected_leader: null,
      });
    });

    it('uses anchors instead of anchor when both are provided', () => {
      anchor = { id: 'anchor1' };
      anchors = Immutable.List([{ id: 'anchor2' }, { id: 'anchor3' }]);
      expect(getFilterParams({ anchor, anchors })).toEqual({
        filters: null,
        a: 'anchor2,anchor3',
        status: null,
        term: null,
        from: null,
        to: null,
        selected_leader: null,
      });
    });

    it('concatenates multiple anchors with commas', () => {
      anchors = Immutable.List([
        { id: 'anchor1' },
        { id: 'anchor2' },
        { id: 'anchor3' },
      ]);
      expect(getFilterParams({ anchors })).toEqual({
        filters: null,
        a: 'anchor1,anchor2,anchor3',
        status: null,
        term: null,
        from: null,
        to: null,
        selected_leader: null,
      });
    });

    it('handles empty anchors list', () => {
      anchors = Immutable.List([]);
      expect(getFilterParams({ anchors })).toEqual({
        filters: null,
        a: null,
        status: null,
        term: null,
        from: null,
        to: null,
        selected_leader: null,
      });
    });

    it('handles undefined/null anchors gracefully', () => {
      anchors = Immutable.List([{ id: 'anchor1' }, null, undefined]);
      expect(getFilterParams({ anchors })).toEqual({
        filters: null,
        a: 'anchor1',
        status: null,
        term: null,
        from: null,
        to: null,
        selected_leader: null,
      });
    });
  });
});
