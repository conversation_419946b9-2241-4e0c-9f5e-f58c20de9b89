jest.unmock('../ReportFiltersReducers');
jest.unmock('redux');
jest.unmock('../initDemographicFilters');
jest.unmock('../parseDateFromServer');
jest.unmock('../getFiltersFromStoreState');

import Immutable from 'immutable';
import reportFiltersRootReducer from '../ReportFiltersReducers';

import {
  FILTERS_APPLY,
  FILTERS_INITIAL_DATA_REQUEST,
  FILTERS_INITIAL_DATA_SUCCESS,
  FILTERS_INITIAL_DATA_FAILURE,
  FILTERS_OPTIONS_UPDATE,
  DEMOGRAPHIC_OPTIONS_DATA_SUCCESS,
} from 'ca-report-filters/actions/ReportFiltersAsyncActions';

import {
  FILTERS_INIT,
  ANCHOR_UPDATE,
  DATE_FILTERS_UPDATE,
  FILTER_CREATE,
  FILTER_UPDATE,
  FILTER_DELETE,
  FILTERS_CLEAR_ALL,
  STATUS_UPDATE,
  SEARCH_TERM_UPDATE,
} from 'ca-report-filters/ReportFiltersActions';

import {
  Anchor,
  DateRangeFilter,
  DemographicFilterRecord,
  Responses,
  Demographic,
  DemographicGroup,
} from '../ReportFilterRecords';

import { REPORT_DATA_SUCCESS } from 'ca-reports/actions/ReportDataAsyncActions';

describe('ReportFiltersReducers', () => {
  let initialState;

  beforeEach(() => {
    initialState = {
      filtersInitialData: Immutable.fromJS({}),
      demographicFilters: Immutable.List(),
      demographics: Immutable.List(),
      anchor: Immutable.fromJS({}),
      selectedStatus: Immutable.fromJS({}),
      searchTerm: Immutable.fromJS({}),
      dateFilters: Immutable.fromJS({}),
      appliedFilters: Immutable.fromJS({}),
      responses: Immutable.fromJS({}),
      config: {},
      dateSummary: Immutable.Map(),
      removeUnreportableDemographicValuesFromFilterFlag: false,
    };
  });

  describe('filtersInitialData', () => {
    it('should set default value', () => {
      initialState.filtersInitialData = undefined;

      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).filtersInitialData
      ).toBeNull();
    });

    it('should return state when handling unknown action type', () => {
      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).filtersInitialData
      ).toEqual(initialState.filtersInitialData);
    });

    it('should handle FILTERS_INITIAL_DATA_REQUEST', () => {
      expect(
        reportFiltersRootReducer(initialState, {
          type: FILTERS_INITIAL_DATA_REQUEST,
        }).filtersInitialData
      ).toEqual(initialState.filtersInitialData);
    });

    it('should handle FILTERS_INITIAL_DATA_SUCCESS', () => {
      const action = {
        type: FILTERS_INITIAL_DATA_SUCCESS,
        payload: {
          initialData: {
            demographicGroups: [
              {
                id: 'a-a',
                text: '0-21',
                options: [
                  {
                    id: '123',
                    text: '18-35',
                    title: '18-35',
                    count: 100,
                    enabled: true,
                  },
                ],
                filterModule: null,
              },
            ],
            anchors: [{ id: 'a-a', text: 'text', hierarchy: true }],
          },
        },
      };

      const demographicOptions = Immutable.List([
        DemographicGroup({
          id: '123',
          text: '18-35',
          title: '18-35',
          count: 100,
          enabled: true,
        }),
      ]);

      const expectedState = Immutable.fromJS({
        initialData: {
          demographicGroups: [
            Demographic({
              id: 'a-a',
              text: '0-21',
              options: demographicOptions,
              loaded: false,
            }),
          ],
          anchors: [Anchor({ id: 'a-a', text: 'text', hierarchy: true })],
        },
      });

      expect(
        reportFiltersRootReducer(initialState, action).filtersInitialData
      ).toEqual(expectedState);
    });

    it('should handle FILTERS_INITIAL_DATA_FAILURE', () => {
      const error = 'some error';

      const expectedState = Immutable.fromJS({
        error: error,
      });

      expect(
        reportFiltersRootReducer(initialState, {
          type: FILTERS_INITIAL_DATA_FAILURE,
          payload: error,
        }).filtersInitialData
      ).toEqual(expectedState);
    });
  });

  describe('demographicFilters', () => {
    beforeEach(() => {
      initialState.demographicFilters = Immutable.List([
        DemographicFilterRecord({
          key: 'a-a',
          demographic: { id: 'a-a' },
          groups: [{ id: 'group-id-a' }],
        }),
        DemographicFilterRecord({
          key: 'b-b',
          demographic: { id: 'b-b' },
          groups: [{ id: 'group-id-b' }],
        }),
      ]);
    });

    describe('FILTER_CREATE action', () => {
      let action;

      beforeEach(() => {
        action = {
          type: FILTER_CREATE,
          payload: {
            filter: {
              demographic: {
                id: 'c-c',
                loaded: true,
              },
              groups: [{ id: 'group-id-c' }],
              loaded: true,
            },
          },
        };
      });

      it('should add item to state', () => {
        const expectedState = initialState.demographicFilters.push(
          DemographicFilterRecord({
            key: 'c-c',
            demographic: { id: 'c-c', loaded: true },
            groups: [{ id: 'group-id-c' }],
            loaded: true,
          })
        );

        expect(
          reportFiltersRootReducer(initialState, action).demographicFilters
        ).toEqual(expectedState);
      });

      it('should set to empty list when groups is not defined', () => {
        const expectedState = initialState.demographicFilters.push(
          DemographicFilterRecord({
            key: 'c-c',
            demographic: { id: 'c-c', loaded: true },
            groups: Immutable.List(),
            loaded: true,
          })
        );

        delete action.payload.filter.groups;

        expect(
          reportFiltersRootReducer(initialState, action).demographicFilters
        ).toEqual(expectedState);
      });
    });

    describe('FILTERS_OPTIONS_UPDATE action', () => {
      let action;

      beforeEach(() => {
        initialState.demographicFilters = Immutable.List([
          DemographicFilterRecord({
            key: 'a-a',
            demographic: Demographic({ id: 'a-a' }),
            groups: [{ id: 'group-id-a' }],
          }),
          DemographicFilterRecord({
            key: 'b-b',
            demographic: Demographic({ id: 'b-b' }),
            groups: [{ id: 'group-id-b' }],
          }),
        ]);

        action = {
          type: FILTERS_OPTIONS_UPDATE,
          payload: {
            id: 'a-a',
            options: [{ id: '1' }],
            text: 'age',
            loaded: true,
          },
        };
      });

      it('updates the demographic options', () => {
        const expectedState = Immutable.List([
          DemographicFilterRecord({
            key: 'a-a',
            demographic: Demographic({
              id: 'a-a',
              text: 'age',
              title: null,
              options: Immutable.List([
                DemographicGroup({
                  id: '1',
                  text: null,
                  title: null,
                  count: null,
                  enabled: null,
                  sort_term: null,
                }),
              ]),
              loaded: true,
            }),
            groups: [{ id: 'group-id-a' }],
            loaded: true,
          }),
          DemographicFilterRecord({
            key: 'b-b',
            demographic: Demographic({
              id: 'b-b',
              loaded: null,
              options: Immutable.List(),
            }),
            groups: [{ id: 'group-id-b' }],
          }),
        ]);

        action = {
          type: FILTERS_OPTIONS_UPDATE,
          payload: {
            id: 'a-a',
            options: [{ id: '1' }],
            text: 'age',
            loaded: true,
            filterModule: null,
          },
        };

        expect(
          reportFiltersRootReducer(initialState, action).demographicFilters
        ).toEqual(expectedState);
      });
    });

    it('should handle unknown action type', () => {
      expect(
        reportFiltersRootReducer(initialState, {
          type: 'unknown',
        }).demographicFilters
      ).toEqual(initialState.demographicFilters);
    });

    it('should set default value when state is undefined', () => {
      initialState.demographicFilters = undefined;

      expect(
        reportFiltersRootReducer(initialState, {
          type: 'unknown',
        }).demographicFilters
      ).toEqual(Immutable.List());
    });

    it('should handle FILTER_UPDATE', () => {
      const action = {
        type: FILTER_UPDATE,
        payload: {
          index: 1,
          filter: DemographicFilterRecord({
            key: 'c-c',
            demographic: { id: 'c-c' },
            groups: [{ id: 'group-id-c' }],
          }),
        },
      };

      const expectedState = Immutable.List([
        initialState.demographicFilters.get(0),
        DemographicFilterRecord({
          key: 'c-c',
          demographic: { id: 'c-c' },
          groups: [{ id: 'group-id-c' }],
        }),
      ]);

      expect(
        reportFiltersRootReducer(initialState, action).demographicFilters
      ).toEqual(expectedState);
    });

    it('should handle FILTERS_CLEAR_ALL', () => {
      const action = {
        type: FILTERS_CLEAR_ALL,
      };

      const expectedState = Immutable.List([]);

      expect(
        reportFiltersRootReducer(initialState, action).demographicFilters
      ).toEqual(expectedState);
    });

    it('should handle FILTER_DELETE', () => {
      const action = {
        type: FILTER_DELETE,
        payload: {
          index: 0,
        },
      };

      const expectedState = Immutable.List([
        DemographicFilterRecord({
          key: 'b-b',
          demographic: { id: 'b-b' },
          groups: [{ id: 'group-id-b' }],
        }),
      ]);

      expect(
        reportFiltersRootReducer(initialState, action).demographicFilters
      ).toEqual(expectedState);
    });

    it('should handle FILTERS_OPTIONS_UPDATE', () => {
      const action = {
        type: FILTERS_OPTIONS_UPDATE,
        payload: {
          id: 'b-b',
          options: [
            {
              count: 157,
              enabled: true,
              id: '5879be5a625f15007e001b09-5879be4f625f15007e001830',
              sort_term: 'IC',
              text: 'IC',
              title: 'IC',
            },
          ],
          text: 'Employee Role',
          filterModule: null,
        },
      };

      const options = Immutable.List([
        DemographicGroup({
          count: 157,
          enabled: true,
          id: '5879be5a625f15007e001b09-5879be4f625f15007e001830',
          sort_term: 'IC',
          text: 'IC',
          title: 'IC',
        }),
      ]);

      const expectedState = Immutable.List([
        DemographicFilterRecord({
          key: 'a-a',
          demographic: { id: 'a-a' },
          groups: [{ id: 'group-id-a' }],
        }),
        DemographicFilterRecord({
          key: 'b-b',
          demographic: Demographic({
            id: 'b-b',
            text: 'Employee Role',
            options: options,
            title: null,
            loaded: true,
          }),
          groups: [{ id: 'group-id-b' }],
          loaded: true,
        }),
      ]);

      expect(
        reportFiltersRootReducer(initialState, action).demographicFilters
      ).toEqual(expectedState);
    });
  });

  describe('demographics', () => {
    describe('DEMOGRAPHIC_OPTIONS_DATA_SUCCESS action', () => {
      let action;

      beforeEach(() => {
        initialState.demographics = Immutable.List([
          Demographic({
            id: 'a-a',
            options: [],
          }),
        ]);

        action = {
          type: DEMOGRAPHIC_OPTIONS_DATA_SUCCESS,
          payload: {
            demographicGroups: [
              {
                id: 'a-a',
                text: 'Age',
                options: [{ id: 'b-b', text: '25-34' }],
              },
            ],
          },
        };
      });

      it('loads the options', () => {
        const expectedState = Immutable.List([
          Demographic({
            id: 'a-a',
            text: null,
            title: null,
            options: Immutable.List([
              DemographicGroup({
                id: 'b-b',
                text: '25-34',
                title: null,
                count: null,
                enabled: null,
                sort_term: null,
              }),
            ]),
            loaded: true,
          }),
        ]);

        expect(
          reportFiltersRootReducer(initialState, action).demographics
        ).toEqual(expectedState);
      });
    });
  });

  describe('anchor', () => {
    it('should handle ANCHOR_UPDATE', () => {
      const anchor = Anchor({
        id: 'ALL_RESULTS',
        text: 'All Results',
      });
      const action = {
        type: ANCHOR_UPDATE,
        payload: {
          anchor,
        },
      };

      expect(reportFiltersRootReducer(initialState, action).anchor).toEqual(
        anchor
      );
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).anchor
      ).toEqual(initialState.anchor);
    });

    it('should set default value', () => {
      initialState.anchor = undefined;

      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).anchor
      ).toBeNull();
    });
  });

  describe('selectedStatus', () => {
    it('should handle ANCHOR_UPDATE', () => {
      const status = 'status';
      const action = {
        type: STATUS_UPDATE,
        payload: {
          status,
        },
      };

      expect(
        reportFiltersRootReducer(initialState, action).selectedStatus
      ).toEqual(status);
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).selectedStatus
      ).toEqual(initialState.selectedStatus);
    });

    it('should set default value', () => {
      initialState.selectedStatus = undefined;

      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).selectedStatus
      ).toBeNull();
    });
  });

  describe('searchTerm', () => {
    it('should handle ANCHOR_UPDATE', () => {
      const searchTerm = 'searchTerm';
      const action = {
        type: SEARCH_TERM_UPDATE,
        payload: {
          searchTerm,
        },
      };

      expect(reportFiltersRootReducer(initialState, action).searchTerm).toEqual(
        searchTerm
      );
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).searchTerm
      ).toEqual(initialState.searchTerm);
    });

    it('should set default value', () => {
      initialState.searchTerm = undefined;

      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).searchTerm
      ).toBe('');
    });
  });

  describe('dateFilters', () => {
    it('should handle DATE_FILTERS_UPDATE', () => {
      const dateFilters = DateRangeFilter({
        default_from_date: new Date(2015, 10, 29),
        default_to_date: new Date(2015, 10, 30),
        from_date: new Date(2015, 10, 29),
        max_date: new Date(2015, 10, 30),
        to_date: new Date(2015, 10, 30),
      });

      const action = {
        type: DATE_FILTERS_UPDATE,
        payload: {
          dateFilters,
        },
      };

      expect(
        reportFiltersRootReducer(initialState, action).dateFilters
      ).toEqual(dateFilters);
    });

    describe('FILTERS_CLEAR_ALL', () => {
      it('should return null when state is null', () => {
        initialState.dateFilters = null;

        expect(
          reportFiltersRootReducer(initialState, {
            type: FILTERS_CLEAR_ALL,
          }).dateFilters
        ).toEqual(initialState.dateFilters);
      });

      it('should update from and to date to be default date', () => {
        initialState.dateFilters = DateRangeFilter({
          default_from_date: new Date(2015, 9, 30),
          default_to_date: new Date(2015, 9, 31),
          from_date: new Date(2015, 10, 30),
          max_date: new Date(2015, 10, 31),
          to_date: new Date(2015, 10, 31),
        });

        const expectedState = DateRangeFilter({
          default_from_date: new Date(2015, 9, 30),
          default_to_date: new Date(2015, 9, 31),
          from_date: new Date(2015, 9, 30),
          max_date: new Date(2015, 10, 31),
          to_date: new Date(2015, 9, 31),
        });

        expect(
          reportFiltersRootReducer(initialState, {
            type: FILTERS_CLEAR_ALL,
          }).dateFilters
        ).toEqual(expectedState);
      });
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).dateFilters
      ).toEqual(initialState.searchTerm);
    });

    it('should set default value', () => {
      initialState.dateFilters = undefined;

      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).dateFilters
      ).toEqual(Immutable.Map());
    });
  });

  describe('appliedFilters', () => {
    it('should handle FILTERS_APPLY', () => {
      const appliedFilters = {
        demographicFilters: 'a',
        dateFilters: 'b',
        anchor: 'c',
        selectedStatus: 'd',
        searchTerm: 'e',
      };

      const action = {
        type: FILTERS_APPLY,
        payload: {
          appliedFilters,
        },
      };

      expect(
        reportFiltersRootReducer(initialState, action).appliedFilters
      ).toEqual(appliedFilters);
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).appliedFilters
      ).toEqual(initialState.appliedFilters);
    });

    it('should set default value', () => {
      initialState.appliedFilters = undefined;

      expect(
        reportFiltersRootReducer(initialState, {
          type: 'random',
        }).appliedFilters
      ).toBeNull();
    });
  });

  describe('responses', () => {
    describe('REPORT_DATA_SUCCESS', () => {
      it('should return response when present', () => {
        const responses = {
          id: 'test',
        };

        const action = {
          type: REPORT_DATA_SUCCESS,
          payload: {
            responses,
          },
        };

        expect(
          reportFiltersRootReducer(initialState, action).responses
        ).toEqual(responses);
      });

      it('should return state when response not present', () => {
        const action = {
          type: REPORT_DATA_SUCCESS,
          payload: {},
        };

        expect(
          reportFiltersRootReducer(initialState, action).responses
        ).toEqual(initialState.responses);
      });
    });

    it('should handle unknown action', () => {
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).responses
      ).toEqual(initialState.responses);
    });

    it('should set default value', () => {
      initialState.appliedFilters = undefined;

      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).responses
      ).toEqual(Immutable.Map());
    });
  });

  describe('config', () => {
    it('should return initial state of config', () => {
      initialState.config = {
        filterCountUrl:
          '/surveys/57145ed8f4761aff35000e9c/reports/admin/count.json',
        filtersUrl: '/surveys/57145ed8f4761aff35000e9c/reports/admin/filters',
        resultsUrl: 'resultsUrl',
        statusOptions: 'statusOptions',
        maxFilters: 5,
      };
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).config
      ).toEqual(initialState.config);
    });

    it('should set default value when state is undefined', () => {
      initialState.config = undefined;
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).config
      ).toEqual({});
    });
  });

  describe('dateSummary', () => {
    it('should return initial state of config', () => {
      initialState.dateSummary = Immutable.Map({ test: 'test' });
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).dateSummary
      ).toEqual(initialState.dateSummary);
    });

    it('should set default value when state is undefined', () => {
      initialState.dateSummary = undefined;
      expect(
        reportFiltersRootReducer(initialState, { type: 'random' }).config
      ).toEqual({});
    });
  });

  describe('initial state', () => {
    let action;
    let groupOptions;

    beforeEach(() => {
      groupOptions = Immutable.List([
        DemographicGroup({
          id: '123',
          text: '0-21',
          title: null,
          count: 10,
          enabled: true,
          sort_term: null,
        }),
      ]);

      action = {
        type: FILTERS_INIT,
        payload: {
          initialData: {
            anchor: Immutable.fromJS({
              id: 'ALL_RESULTS',
              text: 'All Results',
            }),
            dateSummary: Immutable.fromJS({
              dateRange: null,
              timeRange: 'Current Results',
            }),
            demographicFilters: Immutable.fromJS([
              {
                demographic_id: '57a2972e625f15e2b60014c2',
                demographic_label: 'Country',
                filterModule: null,
                group_id: '57a2972e625f15e2b60014c2-57a29722625f15e2b6001217',
                group_label: 'Hong Kong',
              },
              {
                demographic_id: '57a2972e625f15e2b60014c2',
                demographic_label: 'Country',
                filterModule: null,
                group_id: '57a2972e625f15e2b60014c2-57a29722625f15e2b6001216',
                group_label: 'United Kingdom',
              },
            ]),
            demographicGroups: Immutable.fromJS({
              a: [{ id: 'a-a', text: '0-21', count: 5, enabled: true }],
              b: [{ id: 'b-b', text: 'foo', count: 123, enabled: true }],
            }),
            demographics: Immutable.fromJS([
              {
                id: '57a2972e625f15e2b60014be',
                name: 'Age',
                options: groupOptions,
              },
              {
                id: '57a2972e625f15e2b60014c3',
                name: 'City',
                options: groupOptions,
              },
            ]),
            dateFilters: Immutable.fromJS({
              default_from_date: '10 Nov 2015',
              default_to_date: '11 Nov 2015',
              from_date: '10 Nov 2015',
              max_date: '11 Nov 2015',
              to_date: '11 Nov 2015',
            }),
            responses: Immutable.fromJS({
              count: 211,
              comment_significant_population: 10,
              total_responses: 212,
              significant_population: 5,
            }),
            filterCountUrl:
              '/surveys/57145ed8f4761aff35000e9c/reports/admin/count.json',
            filtersUrl:
              '/surveys/57145ed8f4761aff35000e9c/reports/admin/filters',
            hierarchy: false,
            resultsUrl: 'resultsUrl',
            statusOptions: 'statusOptions',
            maxFilters: 5,
            searchTerm: 'searchTerm',
            selectedStatus: 'selectedStatus',
          },
        },
      };
    });

    describe('selectedStatus', () => {
      it('should set selected status when selected status is present', () => {
        expect(
          reportFiltersRootReducer(initialState, action).selectedStatus
        ).toEqual(action.payload.initialData.selectedStatus);
      });

      it('should not set selected status when selected status is not present', () => {
        delete action.payload.initialData.selectedStatus;
        expect(
          reportFiltersRootReducer(initialState, action).selectedStatus
        ).toEqual(null);
      });
    });

    describe('searchTerm', () => {
      it('should set search term when search term is present', () => {
        expect(
          reportFiltersRootReducer(initialState, action).searchTerm
        ).toEqual(action.payload.initialData.searchTerm);
      });

      it('should not set search term when search term is not present', () => {
        delete action.payload.initialData.searchTerm;
        expect(
          reportFiltersRootReducer(initialState, action).searchTerm
        ).toEqual('');
      });
    });

    describe('dateSummary', () => {
      it('should set date summary when date summary is present', () => {
        expect(
          reportFiltersRootReducer(initialState, action).dateSummary
        ).toEqual(action.payload.initialData.dateSummary);
      });

      it('should not set date summary when date summary is not present', () => {
        delete action.payload.initialData.dateSummary;
        expect(
          reportFiltersRootReducer(initialState, action).dateSummary
        ).toEqual(null);
      });
    });

    describe('appliedFilters', () => {
      it('should set applied filters', () => {
        const state = reportFiltersRootReducer(initialState, action);
        expect(state.appliedFilters).toEqual({
          demographicFilters: state.demographicFilters,
          dateFilters: state.dateFilters,
          anchor: state.anchor,
          anchors: Immutable.List(),
          selectedStatus: state.selectedStatus,
          searchTerm: state.searchTerm,
        });
      });
    });

    describe('anchors', () => {
      it('should set anchors when anchors array is provided', () => {
        action.payload.initialData.anchors = Immutable.fromJS([
          { id: 'anchor1', text: 'Anchor 1', hierarchy: false },
          { id: 'anchor2', text: 'Anchor 2', hierarchy: false },
        ]);

        const expectedAnchors = Immutable.List([
          Anchor({ id: 'anchor1', text: 'Anchor 1', hierarchy: false }),
          Anchor({ id: 'anchor2', text: 'Anchor 2', hierarchy: false }),
        ]);

        expect(reportFiltersRootReducer(initialState, action).anchors).toEqual(
          expectedAnchors
        );
      });

      it('should set anchors to empty list when anchors is null', () => {
        action.payload.initialData.anchors = null;
        expect(reportFiltersRootReducer(initialState, action).anchors).toEqual(
          Immutable.List()
        );
      });

      it('should set anchors to empty list when anchors is undefined', () => {
        delete action.payload.initialData.anchors;
        expect(reportFiltersRootReducer(initialState, action).anchors).toEqual(
          Immutable.List()
        );
      });

      it('should set anchors to empty list when anchors is an empty array', () => {
        action.payload.initialData.anchors = Immutable.List();
        expect(reportFiltersRootReducer(initialState, action).anchors).toEqual(
          Immutable.List()
        );
      });
    });

    describe('filtersInitialData', () => {
      it('should set initial data when demographicGroups is set', () => {
        expect(
          reportFiltersRootReducer(initialState, action).filtersInitialData.get(
            'initialData'
          )
        ).toEqual(
          Immutable.fromJS({
            demographicGroups: {
              a: [
                DemographicGroup({
                  id: 'a-a',
                  text: '0-21',
                  count: 5,
                  title: null,
                  enabled: true,
                }),
              ],
              b: [
                DemographicGroup({
                  id: 'b-b',
                  text: 'foo',
                  count: 123,
                  title: null,
                  enabled: true,
                }),
              ],
            },
            anchors: [],
          })
        );
      });

      it('should set initial data with empty map when demographics is null and demographicGroups is not set', () => {
        action.payload.initialData.demographics = null;
        delete action.payload.initialData.demographicGroups;

        expect(
          reportFiltersRootReducer(initialState, action).filtersInitialData.get(
            'initialData'
          )
        ).toEqual(
          Immutable.fromJS({
            demographicGroups: [],
            anchors: [],
          })
        );
      });

      it('should set initial data with empty map when demographics is empty list and demographicGroups is not set', () => {
        action.payload.initialData.demographics = Immutable.List();
        delete action.payload.initialData.demographicGroups;

        expect(
          reportFiltersRootReducer(initialState, action).filtersInitialData.get(
            'initialData'
          )
        ).toEqual(
          Immutable.fromJS({
            demographicGroups: [],
            anchors: [],
          })
        );
      });
    });

    describe('anchor', () => {
      it('should return null when anchor is not present', () => {
        delete action.payload.initialData.anchor;

        expect(reportFiltersRootReducer(initialState, action).anchor).toEqual(
          null
        );
      });

      it('should set anchor', () => {
        expect(reportFiltersRootReducer(initialState, action).anchor).toEqual(
          Anchor({
            id: 'ALL_RESULTS',
            text: 'All Results',
          })
        );
      });
    });

    describe('config', () => {
      it('should set config', () => {
        expect(reportFiltersRootReducer(initialState, action).config).toEqual({
          filterCountUrl:
            '/surveys/57145ed8f4761aff35000e9c/reports/admin/count.json',
          filtersUrl: '/surveys/57145ed8f4761aff35000e9c/reports/admin/filters',
          resultsUrl: 'resultsUrl',
          statusOptions: 'statusOptions',
          maxFilters: 5,
        });
      });

      it('should not set demographics property in config when demographics is not set', () => {
        delete action.payload.initialData.demographics;

        expect(reportFiltersRootReducer(initialState, action).config).toEqual({
          filterCountUrl:
            '/surveys/57145ed8f4761aff35000e9c/reports/admin/count.json',
          filtersUrl: '/surveys/57145ed8f4761aff35000e9c/reports/admin/filters',
          resultsUrl: 'resultsUrl',
          statusOptions: 'statusOptions',
          maxFilters: 5,
        });
      });
    });

    it('should set demographic filters', () => {
      expect(
        reportFiltersRootReducer(initialState, action).demographicFilters.size
      ).toEqual(1);

      expect(
        reportFiltersRootReducer(initialState, action)
          .demographicFilters.get(0)
          .get('demographic')
      ).toEqual(
        Demographic({
          id: '57a2972e625f15e2b60014c2',
          text: 'Country',
          title: null,
        })
      );

      expect(
        reportFiltersRootReducer(initialState, action)
          .demographicFilters.get(0)
          .get('groups')
      ).toEqual(
        Immutable.List([
          DemographicGroup({
            id: '57a2972e625f15e2b60014c2-57a29722625f15e2b6001217',
            text: 'Hong Kong',
            title: null,
            count: null,
          }),
          DemographicGroup({
            id: '57a2972e625f15e2b60014c2-57a29722625f15e2b6001216',
            text: 'United Kingdom',
            title: null,
            count: null,
          }),
        ])
      );
    });

    describe('dateFilters', () => {
      it('should set date filters', () => {
        const expectedState = DateRangeFilter({
          from_date: new Date(2015, 10, 10),
          to_date: new Date(2015, 10, 11),
          max_date: new Date(2015, 10, 11),
          default_from_date: new Date(2015, 10, 10),
          default_to_date: new Date(2015, 10, 11),
        });

        expect(
          reportFiltersRootReducer(initialState, action).dateFilters
        ).toEqual(expectedState);
      });

      it('should set it to null when date filters not present', () => {
        delete action.payload.initialData.dateFilters;

        expect(
          reportFiltersRootReducer(initialState, action).dateFilters
        ).toEqual(null);
      });
    });

    describe('responses', () => {
      it('should set responses', () => {
        expect(
          reportFiltersRootReducer(initialState, action).responses
        ).toEqual(
          Responses({
            count: 211,
            comment_significant_population: 10,
            total_responses: 212,
            significant_population: 5,
          })
        );
      });

      it('should set response to null when response is not present', () => {
        delete action.payload.initialData.responses;

        expect(
          reportFiltersRootReducer(initialState, action).responses
        ).toEqual(null);
      });
    });

    describe('removeUnreportableDemographicValuesFromFilterFlag', () => {
      it('should return the initial value', () => {
        const actionUnderTest = {
          ...action,
          payload: {
            ...action.payload,
            initialData: {
              ...action.payload.initialData,
              removeUnreportableDemographicValuesFromFilterFlag: true,
            },
          },
        };

        expect(
          reportFiltersRootReducer(initialState, actionUnderTest)
            .removeUnreportableDemographicValuesFromFilterFlag
        ).toBe(true);
      });

      it('should return false when initial value undefined', () => {
        const actionUnderTest = {
          ...action,
          payload: {
            ...action.payload,
            initialData: {
              ...action.payload.initialData,
              removeUnreportableDemographicValuesFromFilterFlag: undefined,
            },
          },
        };
        expect(
          reportFiltersRootReducer(initialState, actionUnderTest)
            .removeUnreportableDemographicValuesFromFilterFlag
        ).toBe(false);
      });
    });
  });
});
