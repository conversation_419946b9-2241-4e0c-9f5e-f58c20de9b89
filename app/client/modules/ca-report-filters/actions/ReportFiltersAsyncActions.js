import request from 'ca-request';
import notifyError from 'ca-error-handler/notifyError';
// $FlowFixMe
import Immutable from 'immutable';
import getFiltersFromStoreState from '../getFiltersFromStoreState';
import { getComparisonParamFromStoreState } from 'ca-report-comparison/ComparisonState';
import { getReportFiltersState } from 'ca-report-filters/state/ReportFiltersState';
import setParamsInUrl from 'ca-navigation/setParamsInUrl';
import getFilterParams from 'ca-report-filters/getFilterParams';
import { updateDateFilters } from 'ca-report-filters/ReportFiltersActions';

export const FILTERS_APPLY = 'FILTERS_APPLY';
export const FILTERS_OPTIONS_UPDATE = 'FILTERS_OPTIONS_UPDATE';
export const FILTERS_INITIAL_DATA_REQUEST = 'FILTERS_INITIAL_DATA_REQUEST';
export const FILTERS_INITIAL_DATA_SUCCESS = 'FILTERS_INITIAL_DATA_SUCCESS';
export const FILTERS_INITIAL_DATA_FAILURE = 'FILTERS_INITIAL_DATA_FAILURE';

export const DEMOGRAPHIC_OPTIONS_DATA_REQUEST =
  'DEMOGRAPHIC_OPTIONS_DATA_REQUEST';
export const DEMOGRAPHIC_OPTIONS_DATA_SUCCESS =
  'DEMOGRAPHIC_OPTIONS_DATA_SUCCESS';
export const DEMOGRAPHIC_OPTIONS_DATA_FAILURE =
  'DEMOGRAPHIC_OPTIONS_DATA_FAILURE';
export const DEMOGRAPHIC_LOADED = 'DEMOGRAPHIC_LOADED';
export const ANCHOR_OPTIONS_DATA_REQUEST = 'ANCHOR_OPTIONS_DATA_REQUEST';
export const ANCHOR_OPTIONS_DATA_FAILURE = 'ANCHOR_OPTIONS_DATA_FAILURE';
export const ANCHOR_OPTIONS_DATA_SUCCESS = 'ANCHOR_OPTIONS_DATA_SUCCESS';
export const ANCHOR_OPTIONS_UPDATE = 'ANCHOR_OPTIONS_UPDATE';

export function createActions(dependencies = {}) {
  const {
    applyFiltersImplementation,
    includeComparison, // whether to also apply comparison when applying filters
  } = dependencies;

  // TODO: come up with a common abstraction for async actions
  const ReportFiltersAsyncActions = {
    requestFiltersInitialData() {
      return {
        type: FILTERS_INITIAL_DATA_REQUEST,
      };
    },
    receiveFiltersInitialData(initialData) {
      return {
        type: FILTERS_INITIAL_DATA_SUCCESS,
        payload: {
          initialData,
        },
      };
    },
    errorFiltersInitialData(err) {
      return {
        type: FILTERS_INITIAL_DATA_FAILURE,
        payload: err,
        error: true,
      };
    },
    requestDemographicOptions(demographic) {
      return {
        type: DEMOGRAPHIC_OPTIONS_DATA_REQUEST,
        payload: demographic,
      };
    },
    requestAnchorOptions(anchor) {
      return {
        type: ANCHOR_OPTIONS_DATA_REQUEST,
        payload: anchor,
      };
    },
    receiveAnchorOptions(anchorData) {
      return {
        type: ANCHOR_OPTIONS_DATA_SUCCESS,
        payload: anchorData,
      };
    },
    receiveDemographicOptions(initialData) {
      return {
        type: DEMOGRAPHIC_OPTIONS_DATA_SUCCESS,
        payload: initialData,
      };
    },
    errorDemographicOptions(err) {
      return {
        type: DEMOGRAPHIC_OPTIONS_DATA_FAILURE,
        payload: err,
        error: true,
      };
    },
    errorAnchorOptions(err) {
      return {
        type: ANCHOR_OPTIONS_DATA_FAILURE,
        payload: err,
        error: true,
      };
    },
    setDemographicLoaded(demographic) {
      return {
        type: DEMOGRAPHIC_LOADED,
        payload: demographic,
      };
    },
    applyFilters() {
      return (dispatch, getState) => {
        const rootState = getState();

        const filtersToApply = getFiltersFromStoreState(
          getReportFiltersState(rootState)
        );
        const comparisonToApply = includeComparison
          ? getComparisonParamFromStoreState(rootState)
          : {};
        const appliedFilters = { ...filtersToApply, ...comparisonToApply };

        dispatch({
          type: FILTERS_APPLY,
          payload: {
            appliedFilters: appliedFilters,
          },
        });

        dispatch(applyFiltersImplementation(appliedFilters));
      };
    },
    updateDemographicFilters(payload) {
      return {
        type: FILTERS_OPTIONS_UPDATE,
        payload: payload,
      };
    },
    loadDemographicOptions(
      demographicId,
      restrictedDemographicGroups = Immutable.List()
    ) {
      return (dispatch, getState) => {
        const { config, appliedFilters, demographicFilters, anchors } =
          getReportFiltersState(getState());

        const filterParams = getFilterParams({
          anchors,
          ...appliedFilters,
          demographicFilters: restrictedDemographicGroups.size
            ? demographicFilters.filter((d) =>
                restrictedDemographicGroups.includes(d.demographic.id)
              )
            : demographicFilters,
        });

        const optionsUrl = setParamsInUrl(config.filtersUrl, {
          ...filterParams,
          demographic: demographicId,
        });

        return request(optionsUrl)
          .then((res) => res.json())
          .then((data) => {
            const filterData = {
              demographicGroups: data.options || {},
            };

            dispatch(
              ReportFiltersAsyncActions.receiveDemographicOptions(filterData)
            );
            dispatch(
              ReportFiltersAsyncActions.updateDemographicFiltersOptions(
                filterData
              )
            );
          })
          .catch((err) => {
            notifyError(err, { filtersUrl: config.filtersUrl });
            dispatch(ReportFiltersAsyncActions.errorDemographicOptions(err));
          });
      };
    },
    loadAnchorOptions(anchor) {
      return (dispatch, getState) => {
        const { config } = getReportFiltersState(getState());

        dispatch(ReportFiltersAsyncActions.requestAnchorOptions(anchor));

        const optionsUrl = setParamsInUrl(config.filtersUrl, {
          demographic: anchor.id,
        });
        return request(optionsUrl)
          .then((res) => res.json())
          .then((data) => {
            const filterData = {
              anchorOptions: data.anchor || {},
            };

            dispatch(
              ReportFiltersAsyncActions.receiveAnchorOptions(filterData)
            );
            dispatch(
              ReportFiltersAsyncActions.updateAnchorFilterOptions(filterData)
            );
          })
          .catch((err) => {
            notifyError(err, { filtersUrl: config.filtersUrl });
            dispatch(ReportFiltersAsyncActions.errorAnchorOptions(err));
          });
      };
    },
    loadFiltersInitialData() {
      return (dispatch, getState) => {
        const { filtersInitialData, config } = getReportFiltersState(
          getState()
        );

        if (filtersInitialData.get('initialData')) return;

        dispatch(ReportFiltersAsyncActions.requestFiltersInitialData());
        return request(config.filtersUrl)
          .then((res) => res.json())
          .then((data) => {
            const initialData = {
              demographicGroups: data.options || {},
              anchors: data.anchor_reports_data || data.anchor || [],
            };

            dispatch(
              ReportFiltersAsyncActions.receiveFiltersInitialData(initialData)
            );
            dispatch(
              ReportFiltersAsyncActions.updateDemographicFiltersOptions(
                initialData
              )
            );
          })
          .catch((err) => {
            notifyError(err, { filtersUrl: config.filtersUrl });
            dispatch(ReportFiltersAsyncActions.errorFiltersInitialData(err));
          });
      };
    },
    updateDemographicFiltersOptions(initialData) {
      return (dispatch, getState) => {
        const { demographicFilters } = getReportFiltersState(getState());

        const currentDemographicFilterIds = demographicFilters.map(
          (filter) => filter.demographic.id
        );

        const getMatchingDemographicFilters =
          initialData.demographicGroups.filter((demographicFilter) =>
            currentDemographicFilterIds.includes(demographicFilter.id)
          );

        getMatchingDemographicFilters.map((filter) => {
          filter.loaded = true;
          dispatch(ReportFiltersAsyncActions.updateDemographicFilters(filter));
        });
      };
    },
    updateAnchorFilterOptions(anchorData) {
      return {
        type: ANCHOR_OPTIONS_UPDATE,
        payload: anchorData,
      };
    },
    refreshDemographicOptions(restrictedDemographicGroups) {
      return (dispatch, getState) => {
        const { demographicFilters } = getReportFiltersState(getState());
        demographicFilters.forEach((filter) => {
          dispatch(
            ReportFiltersAsyncActions.loadDemographicOptions(
              filter.demographic.id,
              restrictedDemographicGroups
            )
          );
        });
      };
    },
    updateDateFiltersAndRefreshDemographicOptions(
      dateFilters,
      restrictedDemographicGroups
    ) {
      return (dispatch) => {
        /*
        Update the date filter and then apply filters so that it will be picked up by
        refreshDemographicOptions() action
        */
        dispatch(updateDateFilters(dateFilters));
        dispatch(ReportFiltersAsyncActions.applyFilters());
        dispatch(
          ReportFiltersAsyncActions.refreshDemographicOptions(
            restrictedDemographicGroups
          )
        );
      };
    },
  };

  return ReportFiltersAsyncActions;
}

export const { refreshDemographicOptions } = createActions();
