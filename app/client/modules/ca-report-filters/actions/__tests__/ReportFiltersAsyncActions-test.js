jest.unmock('../ReportFiltersAsyncActions');
jest.unmock('../../state/ReportFiltersState');

import * as ReportFiltersAsyncActions from '../ReportFiltersAsyncActions';

import Immutable from 'immutable';
import request from 'ca-request';
import createMockedReduxStore from 'ca-test-utils/redux/createMockedReduxStore';

import getFiltersFromStoreState from '../../getFiltersFromStoreState';
import { getComparisonParamFromStoreState } from 'ca-report-comparison/ComparisonState';

import REPORT_FILTERS from 'ca-report-filters/namespace';

import getLastDispatchedActionsWithType from 'ca-test-utils/redux/getLastDispatchedActionsWithType';

jest.mock('ca-request/request');
jest.mock('../../getFiltersFromStoreState');
jest.mock('ca-report-comparison/ComparisonState');
jest.mock('ca-error-handler/notifyError');
jest.mock('ca-navigation/setParamsInUrl', () =>
  jest.fn(
    (url, params) =>
      `${url}?${Object.keys(params)
        .map((key) => `${key}=${params[key]}`)
        .join('&')}`
  )
);

describe('ReportFiltersAsyncActions', () => {
  let store;

  let applyFiltersImplementation;
  let stateSelect;

  beforeEach(() => {
    store = createMockedReduxStore();
    applyFiltersImplementation = jest.fn();

    stateSelect = (state) => state;
  });

  describe('loadFiltersInitialData', () => {
    let jsonResponse;
    let filtersUrl;

    beforeEach(() => {
      filtersUrl = '/filters';

      jsonResponse = {
        options: {
          id: '1',
        },
        anchor_reports_data: ['asd'],
      };

      request.__respond(jsonResponse);

      store.getState.mockReturnValue({
        [REPORT_FILTERS]: {
          config: {
            filtersUrl,
          },
          filtersInitialData: Immutable.fromJS({
            demographicGroups: null,
            anchors: null,
            fetching: false,
          }),
        },
      });
    });

    it('loads the data', async () => {
      const { loadFiltersInitialData } =
        ReportFiltersAsyncActions.createActions(
          { applyFiltersImplementation },
          stateSelect
        );
      store.dispatch(loadFiltersInitialData());

      await store.__waitForAllAsyncActions();

      const requestAction = getLastDispatchedActionsWithType(
        store.dispatch,
        ReportFiltersAsyncActions.FILTERS_INITIAL_DATA_REQUEST
      );
      expect(requestAction).not.toBeUndefined();

      expect(request).toBeCalledWith(filtersUrl);
      const receiveAction = getLastDispatchedActionsWithType(
        store.dispatch,
        ReportFiltersAsyncActions.FILTERS_INITIAL_DATA_SUCCESS
      );

      expect(receiveAction.payload.initialData.demographicGroups).toEqual(
        jsonResponse.options
      );
      expect(receiveAction.payload.initialData.anchors).toEqual(
        jsonResponse.anchor_reports_data
      );
    });
  });

  describe('applyFilters', () => {
    it('applies the filters', () => {
      const storeState = {};
      store.getState.mockReturnValue({ [REPORT_FILTERS]: storeState });

      const applyFiltersImplementationAction = {};
      applyFiltersImplementation.mockReturnValue(
        applyFiltersImplementationAction
      );

      const filtersToApply = { filters: 'filters' };
      getFiltersFromStoreState.mockReturnValue(filtersToApply);

      const comparisonToApply = { comparison: 'comparison' };
      getComparisonParamFromStoreState.mockReturnValue(comparisonToApply);

      const { applyFilters } = ReportFiltersAsyncActions.createActions(
        { applyFiltersImplementation, includeComparison: true },
        stateSelect
      );
      const applyFiltersThunk = applyFilters();
      applyFiltersThunk(store.dispatch, store.getState);

      const applyFiltersAction = getLastDispatchedActionsWithType(
        store.dispatch,
        ReportFiltersAsyncActions.FILTERS_APPLY
      );
      expect(applyFiltersAction).not.toBeUndefined();
      expect(applyFiltersAction.payload.appliedFilters).toEqual({
        ...filtersToApply,
        ...comparisonToApply,
      });
      expect(applyFiltersImplementation).toBeCalledWith({
        ...filtersToApply,
        ...comparisonToApply,
      });
      expect(store.dispatch).toBeCalledWith(applyFiltersImplementationAction);
    });
  });

  describe('loadDemographicOptions', () => {
    let filtersUrl;
    let demographicId;
    let jsonResponse;

    beforeEach(() => {
      filtersUrl = '/filters';
      demographicId = 'demo123';

      jsonResponse = {
        options: [
          {
            id: 'demo123',
            text: 'Department',
            options: [{ id: 'opt1', text: 'Engineering', count: 100 }],
          },
        ],
      };

      request.__respond(jsonResponse);

      store.getState.mockReturnValue({
        [REPORT_FILTERS]: {
          config: {
            filtersUrl,
          },
          demographicFilters: Immutable.List([
            { demographic: { id: 'demo123' } },
          ]),
          anchors: Immutable.List(),
          appliedFilters: {},
        },
      });
    });

    it('loads demographic options without anchors', async () => {
      const { loadDemographicOptions } =
        ReportFiltersAsyncActions.createActions(
          { applyFiltersImplementation },
          stateSelect
        );

      store.dispatch(loadDemographicOptions(demographicId));
      await store.__waitForAllAsyncActions();

      // Updated expectation to match the actual URL format being used
      expect(request).toHaveBeenCalledWith(
        expect.stringMatching(/demographic=demo123/)
      );

      const successAction = getLastDispatchedActionsWithType(
        store.dispatch,
        ReportFiltersAsyncActions.DEMOGRAPHIC_OPTIONS_DATA_SUCCESS
      );

      expect(successAction).toBeDefined();
      expect(successAction.payload.demographicGroups).toEqual(
        jsonResponse.options
      );
    });

    it('loads demographic options with anchors', async () => {
      const anchors = Immutable.List([
        { id: 'anchor1-option1', text: 'Anchor 1' },
        { id: 'anchor2-option2', text: 'Anchor 2' },
      ]);

      store.getState.mockReturnValue({
        [REPORT_FILTERS]: {
          config: {
            filtersUrl,
          },
          demographicFilters: Immutable.List([
            { demographic: { id: 'demo123' } },
          ]),
          anchors: anchors,
          appliedFilters: {},
        },
      });

      const { loadDemographicOptions } =
        ReportFiltersAsyncActions.createActions(
          { applyFiltersImplementation },
          stateSelect
        );

      store.dispatch(loadDemographicOptions(demographicId));
      await store.__waitForAllAsyncActions();

      // Verify the URL contains anchor parameters
      expect(request).toHaveBeenCalledWith(
        expect.stringContaining(`${filtersUrl}?`)
      );
      expect(request).toHaveBeenCalledWith(
        expect.stringMatching(/a=anchor1-option1,anchor2-option2/)
      );
      expect(request).toHaveBeenCalledWith(
        expect.stringMatching(/demographic=demo123/)
      );
    });

    it('restricts demographic groups when restrictedDemographicGroups is provided', async () => {
      const restrictedDemographicGroups = Immutable.List(['demo123']);
      const demographicFilters = Immutable.List([
        { demographic: { id: 'demo123' } },
        { demographic: { id: 'demo456' } },
      ]);

      store.getState.mockReturnValue({
        [REPORT_FILTERS]: {
          config: {
            filtersUrl,
          },
          demographicFilters: demographicFilters,
          anchors: Immutable.List(),
          appliedFilters: {},
        },
      });

      const { loadDemographicOptions } =
        ReportFiltersAsyncActions.createActions(
          { applyFiltersImplementation },
          stateSelect
        );

      store.dispatch(
        loadDemographicOptions(demographicId, restrictedDemographicGroups)
      );
      await store.__waitForAllAsyncActions();

      // Only the first demographic filter should be included (the restricted one)
      // Update the expectation to match the actual URL pattern
      expect(request).toHaveBeenCalledWith(
        expect.stringMatching(
          /a=anchor1-option1,anchor2-option2&.*demographic=demo123/
        )
      );

      // Verify no other demographic IDs were included
      expect(request).not.toHaveBeenCalledWith(
        expect.stringMatching(/demographic=demo456/)
      );
    });

    it('handles error responses', async () => {
      const error = new Error('Network error');
      // Fix: use Promise.reject instead of a non-existent __reject method
      request.mockImplementationOnce(() => Promise.reject(error));

      const { loadDemographicOptions } =
        ReportFiltersAsyncActions.createActions(
          { applyFiltersImplementation },
          stateSelect
        );

      store.dispatch(loadDemographicOptions(demographicId));
      await store.__waitForAllAsyncActions();

      const errorAction = getLastDispatchedActionsWithType(
        store.dispatch,
        ReportFiltersAsyncActions.DEMOGRAPHIC_OPTIONS_DATA_FAILURE
      );

      expect(errorAction).toBeDefined();
      expect(errorAction.payload).toBe(error);
      expect(errorAction.error).toBe(true);
    });
  });
});
