import pick from 'lodash/pick';
import { FILTERS_STATE_KEYS } from './constants';
import { IncompleteRootState } from 'ca-report-filters/connectReportFiltersBlock';

export default function getFiltersFromStoreState(
  state: IncompleteRootState
): Pick<
  IncompleteRootState,
  | 'demographicFilters'
  | 'dateFilters'
  | 'anchor'
  | 'anchors'
  | 'selectedStatus'
  | 'searchTerm'
> {
  // @ts-expect-error this is kinda annoying, the typing changes it to a partial when it doesn't have to be
  return pick(state, FILTERS_STATE_KEYS);
}
