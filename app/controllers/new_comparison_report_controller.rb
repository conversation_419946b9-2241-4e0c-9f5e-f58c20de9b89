class NewComparisonReportController < ReportsController
  component :report
  @model_id_name = :survey_id

  REPORT_TYPE = :comparison
  include MultiFormatReport

  before_action do
    new_reporting_url = SurveyReporting::Queries::NewReportingFrontEndUrl.new.call(survey_id: survey_id, report_id: report_id, user_id: @context.user.aggregate_id, report_type: REPORT_TYPE)
    if !request.path.ends_with?(".json") && !request.path.ends_with?(".csv") && !request.path.ends_with?(".xls") && !is_spreadsheet_export_request? && new_reporting_url.present?
      url_path = construct_redirect_url_path
      redirect_to new_reporting_url + url_path
    end
  end

  expose(:report_data_service) do
    query_params = Query::ReportingParameters.with_heatmap_summary_id(context, report_date_range, context.filters(false), heatmap_comparison_id, full_reporting_line: full_reporting_line?, hierarchy_privacy: hierarchy_privacy?)

    create_report_data_service(query_params)
  end

  expose(:report_data) do
    Reports::ComparisonReport::Builder.new(
      survey: @context.survey,
      report_data_service: report_data_service,
      data_lines: build_data_lines,
      context: @context,
      paths: paths,
      max_filters: max_filters,
      questions_for_reports: questions_for_reports,
      factors_for_reports: factors_for_reports,
      score_type: score_type,
      contains_insignificant_filters: filter_significance_result.contains_insignificant_filters?,
      admin_report: admin_report?,
      selected_comparison: heatmap_comparison,
      report_id: report_id
    )
      .comparison_report_props
      .to_hash
  end

  expose(:export_data) { Export::HeatmapExportData.new(report_data, score_type) }

  def show
    respond_to do |format|
      format.any(:html) do
        render_report
      end
      format.json do
        render json: report_props[:reports]
      end
      format.csv do
        content = comparison_report_to_csv
        send_data content, type: "application/csv; charset=iso-8859-1; header=present", disposition: "attachment; filename=#{export_filename}.csv"
      end
      format.any(:xls, :xlsx) do
        spreadsheet = Export::DemographicHeatmap.new(export_data, results_title, "Custom Report", score_type, params).generate
        if spreadsheet.present?
          send_data spreadsheet.to_stream.read, filename: "#{export_filename}.xlsx", type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        end
      end
    end
  end

  # GET ./count
  # Returns the count associated with a filter
  def count
    context.filters = raw_valid_filters(context) # Use all filters, even though they are usually removed for the custom heatmap
    overall_participation_result = report_data_service.overall_participation_result

    if overall_participation_result.error?
      return render json: {
        count: -1,
        total_responses: -1,
        significant_population: reporting_rules.significance
      }
    end

    count = overall_participation_result.data.filtered_result.submission_count
    count = -1 if count.present? && count < reporting_rules.significance || filter_significance_result.contains_insignificant_filters?

    render json: {
      count: count,
      total_responses: overall_participation_result.data.overall_result.submission_count,
      significant_population: reporting_rules.significance,
      contains_insignificant_filters: filter_significance_result.contains_insignificant_filters?
    }.to_json
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(
      survey: survey
    )
  end

  def results_title
    "Results for the #{@context.survey.name_only} Survey"
  end

  def comparison_report_to_csv
    CSV.generate(col_sep: ",", quote_char: '"') do |csv|
      Export::HeatmapCsv.new(export_data, results_title).generate_rows.each { |row| csv << row }
    end
  end

  # TODO: move this
  def build_data_lines
    [baseline_data_line] + data_lines_from_params
  end

  # TODO: move this
  def baseline_data_line
    filter_configuration = Query::FilterConfiguration.for_context(@context, @context.filters(false), full_reporting_line: full_reporting_line?)
    baseline_name = construct_baseline_name
    Reporting::Data::CustomQuery::DataLine.with(
      index: 0,
      baseline: true,
      name: I18n.t("reports.heatmap.heatmap_table.baseline", baseline_name: baseline_name),
      filters: filter_configuration.direct_filters,
      hierarchy_filters: filter_configuration.hierarchical_filters
    )
  end

  def data_lines_from_params
    (0..Props::ComparisonReportProps::MAX_DATA_LINES)
      .reject { |index| params["#{index}-name"].nil? }
      .map do |index|
      name = params["#{index}-name"]

      filter_param = params["#{index}-filters"]
      anchor_param = params["#{index}-a"]

      filters = parse_comparison_filters(filter_param)
      valid_filters = current_user.validate_dissect_filters(@context.survey, @scope, filters)

      filter_configuration = Query::FilterConfiguration.for_context(
        @context,
        valid_filters,
        full_reporting_line: full_reporting_line?
      )

      Reporting::Data::CustomQuery::DataLine.with(
        index: index + 1,
        baseline: false,
        name: name,
        filters: filter_configuration.direct_filters,
        hierarchy_filters: hierarchy_filters(anchor_param)
      )
    end
  end

  def hierarchy_filters(dataline_anchor_param)
    dataline_anchor = dataline_anchor_param.present? ?
                        parse_filter_parameter(@context.survey, dataline_anchor_param) :
                        Array.wrap(@context.filter_anchors).first || @context.filter_anchor
    return [] unless dataline_anchor.hierarchy?

    [Query::FilterConfiguration.construct_anchor(dataline_anchor)]
  end

  def parse_comparison_filters(filter_param)
    result = []

    # Limit to the configurable amount
    filter_params = filter_param.to_s.split(",")[0..(max_filters - 1)]

    filter_params.map(&:strip).each do |parameter|
      filter = parse_filter_parameter(@context.survey, parameter)
      result << filter unless filter.nil?
    end
    result | @context.filters(false)
  end

  def establish_legacy_report_scope
    comparison_report_scope
  end

  def max_filters
    @max_filters ||= @context.survey.config(Configs::MAXIMUM_REPORT_FILTERS)
  end

  def score_type
    params[:s] == "p" ? :percentage : :delta
  end

  def heatmap_comparison
    @heatmap_comparison ||= Comparison.new(@context, heatmap_comparison_id)
  end

  def heatmap_comparison_id
    return Context::COMPARISON_KEY_OVERALL if params[:heatmap_comparison_id].blank?

    params[:heatmap_comparison_id]
  end

  def construct_redirect_url_path
    url = "new_comparison_report"
    query_params = request.query_parameters
    if query_params.any?
      url = url + "?" + query_params.to_query
    end
    url
  end

  def construct_baseline_name
    display_name = if has_access_to_view_multi_demographic_reports?(
      account_id: @context.account.aggregate_id,
      user_id: @context.user.aggregate_id,
      survey_id: @context.survey.aggregate_id
    )
      @context.anchors.map(&:display_name).join(" + ")
    else
      @context.anchor&.display_name
    end

    display_name || I18n.t("reports.custom.column_headings.all_results")
  end
end
