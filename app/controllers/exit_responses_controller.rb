class ExitResponsesController < ApplicationController
  include ResponseHelper
  include Redirectable
  include ReportAccess
  include HasReportProps
  include ApplicationProps

  # This controller checks a permission (CREATE_SURVEY + WRITE_COMMENT_REPLIES) that can be explicitly assigned to an employee
  # as a permit in the permissions service. Including this helper here will ensure that the permission
  # service is called during authorization checks.
  include PermissionsServicePermits

  REPORT_TYPE = :activity

  allows_access_to :all_account_members ## however there are other filters that do additional authentication
  component :exit

  layout false, only: :count

  expose(:lifecycle_admin) { current_user.can_administer_lifecycle?(survey) }
  expose(:can_administer_account) { current_user.can_administer_account?(account) }
  expose(:can_administer_survey) { current_user.can_administer_survey?(survey) }
  expose(:survey) { survey_in_context }
  expose(:account) { account_in_context }
  expose(:survey_topic) { survey_topic_in_context }
  expose(:responses) { paginated_responses }
  expose(:filtered_responses) { responses_in_context } # Used in helper methods until we refactor it out
  expose(:all_responses) { unfiltered_responses } # Used in helper methods until we refactor it out
  expose(:people) { people_in_context }
  expose(:employee) { account.people.find_by(aggregate_id: params[:employee_id]) }
  expose(:reviewer) { survey.full_workflow? ? account.people.find_by(aggregate_id: params[:reviewer_id]) : nil }
  expose(:context) { Context.new(account: account, survey: survey, user: current_user, response: survey_topic) }
  expose(:valid_sections) { valid_sections_for_survey(survey) }
  expose(:filters_template) { "/reports/exit_filters" }
  expose(:report_type) { REPORT_TYPE }
  expose(:header_props) { construct_header_props }
  expose(:report_sharing?) { survey.report_sharing_enabled? }
  expose(:raw_extractable) { false }
  expose(:current_interviewer) { current_user == survey_topic.interviewer }
  expose(:questions_for_reports) { QuestionsForReports.new(survey) }
  expose(:activity_report_title_block_props) { construct_activity_report_title_block_props }
  expose(:total_no_of_people) { construct_total_no_of_people }
  expose(:participant_subtitle_props) { construct_participant_subtitle_props }

  before_action only: [:create, :user_search] do
    render_forbidden unless lifecycle_admin || current_interviewer
  end

  before_action :save_user_focus

  def demo_mode
    render_forbidden unless survey.demo_mode?
  end

  def index
    return redirect_to three_sixty_paths.index if survey.three_sixty?

    if xhr?
      return render template: "exit_responses/_response_list", layout: false
    end

    @content_heading = I18n.t("reports.lifecycle.content_headings.activity_report")

    render "index"
  end

  def user_search
    render "user_search", formats: [:js], layout: false
  end

  def create
    set_survey_topic_params

    last_day_of_employment = DateMethods.extract_date_from_param(params[:last_day_of_employment], employee.end_date)

    unless valid_params?(last_day_of_employment: last_day_of_employment)
      if xhr?
        render_error
      else
        redirect_to survey_exit_responses_path(survey)
      end
      return
    end

    employee_role = account.user_types.where(_id: params[:employee_role]).first
    unless employee_role.nil? || employee.user_type_ids.include?(employee_role.id)
      employee.user_types << employee_role
      employee.save!
    end

    Surveys::Commands::StartLifecycleProcess.new.call(survey_topic: survey_topic, correlation_id: SecureRandom.uuid)

    if survey.exit?
      errors = deactivate_employee(employee, last_day_of_employment)
      raise "Failed to deactivate employee #{employee.aggregate_id} #{errors.inspect}" if errors.any?
    end

    # Check if we need to redirect to the response, or show the classify
    redirect = get_redirect
    # TICKET 321 https://bitbucket.org/cultureamp/murmur/issue/321/redirects-should-always-be-a-relative-path
    # Should check the redirect is a relative path

    if redirect.present?
      redirect_to(redirect)
    elsif xhr?
      render partial: "show_classify", locals: {survey_topic: survey_topic, survey: survey}
    else
      redirect_to survey_exit_responses_path(survey)
    end
  end

  def destroy
    survey_topic.delete!
    redirect_to Navigation::LifecyclePaths.new(survey, current_user).index
  end

  def show
    return render_not_found if survey.deleted?
    return render :deleted if survey_topic.deleted?
    return redirect_to three_sixty_paths.get(survey_topic) if survey.three_sixty?
    return render_forbidden unless process_actions(survey_topic).include?(:view)

    survey = survey_topic.survey
    answers = survey_topic.subject_response.answers.answerable_when_submitted
    @response = answers_for_user_type(answers.culture, survey_topic.subject) + answers_for_user_type(answers.reviewer_notes, survey_topic.subject)
    @interview = answers_for_user_type(answers.interview, survey_topic.subject)
    @classify = answers.classify

    segment_stq_ids = survey.survey_to_questions.filter(:segment).map(&:id)
    @segment = answers.select { |a| segment_stq_ids.include?(a.survey_to_question_id) }
    @outcome = answers.outcome
    @section = param_to_sym(:section, valid_sections) || :response
    @content_heading = I18n.t("reports.lifecycle.content_headings.#{@section.to_s.downcase}") || "Response"

    @employee = survey_topic.subject
    @reviewer = survey_topic.interviewer

    @response_detail_props = construct_response_detail_props(employee_name: @employee.name)
  end

  def update
    return render_forbidden unless process_actions(survey_topic).include?(:update)

    lifecycle_paths = Navigation::LifecyclePaths.new(survey, current_user)
    redirect = lifecycle_paths.get(survey_topic)

    case params[:field_name]
    when "reopen"
      survey_topic.reopen!

    when "skip"
      survey_topic.skip_subject!(as_user: current_user)

    when "close"
      survey_topic.close!
      # Is this survey in demo mode? If so, show the demo page. If not, proceed with the usual workflow (status page)
      redirect =
        if survey.demo_mode? && lifecycle_admin
          demo_exit_responses_path(survey_topic)
        else
          lifecycle_paths.index
        end

    when "interview-close"
      survey_topic.complete_interview!

    when "interview-reopen"
      survey_topic.reopen_interview!

    when "manager"
      select_manager
      redirect = survey_exit_responses_path(survey)

    when "classify"
      classify

      # Is this survey in demo mode? If so, show the demo page. If not, proceed with the usual workflow (status page)
      redirect = survey_exit_responses_path(survey)
      redirect = demo_exit_responses_path(survey_topic) if survey.demo_mode? && lifecycle_admin
    end

    if xhr?
      head :ok
    elsif redirect.present?
      redirect_to redirect
    end
  end

  def construct_participant_subtitle_props
    Props::Participant::FilterProps.new(participant_filter: survey.participant_filter).to_hash
  end

  private

  def get_survey_configure_path
    configure_path = Navigation::SurveyLinks.new(survey: survey, user: current_user).configure
    unless configure_path.nil?
      return configure_path[:path]
    end

    ""
  end

  def title_block_nav_items
    return title_block_nav_items_for_survey_admin if can_administer_survey

    activity_path = paths.survey_exit_responses_path(survey.id)
    nav_items = [{text: I18n.translate("navigation.activity"), href: activity_path, active: true}]

    report_access_grant = current_user.report_access_grants.detect { |grant| grant.survey == survey }
    return nav_items unless report_access_grant

    # If user has at least one access grant, they can reasonably expect to be able to navigate to their viewable reports
    # so here we manually construct navigation links to other reports they can view.
    # https://cultureamp.atlassian.net/jira/software/projects/KR/boards/185?selectedIssue=KR-34
    report = report_access_grant.report
    question_report_path = paths.survey_report_question_insight_report_index_path(survey.id, report.id)
    lifecycle_insight_report_path = paths.survey_report_lifecycle_insight_report_path(survey.id, report.id)
    demographics_report_path = paths.survey_report_demographics_path(survey.id, report.id)
    comments_report_path = paths.survey_report_comments_path(survey.id, report.id)
    base_demographic_stq = report.base_demographic_stq

    nav_items.concat([
      {text: I18n.translate("navigation.lifecycle_insight_report"), href: lifecycle_insight_report_path, active: false},
      {text: I18n.translate("navigation.questions"), href: question_report_path, active: false}
    ])

    # advanced report has access to heatmap
    if report.advanced?
      nav_items.push({text: I18n.translate("navigation.demographics"), href: demographics_report_path, active: false})
    end

    if report.show_comments?
      nav_items.push({text: I18n.translate("navigation.comments"), href: comments_report_path, active: false})
    end

    # We are sending these nav items without anchor because for "All Results" report, there will not be any base demographic stq
    return nav_items unless base_demographic_stq

    select_option = report_access_grant.select_option
    filter = ReportFilter.new(base_demographic_stq, select_option, true).to_s

    nav_items.each do |item|
      item[:href] = "#{item[:href]}?a=#{filter}" unless item[:text] == I18n.translate("navigation.activity")
    end

    nav_items
  end

  def title_block_nav_items_for_survey_admin
    set_items = [:summaryInsightReport, :activity, :lifecycleInsight, :questions, :demographics, :textAnalytics]
    items_from_props = all_survey_topics_props[:links]

    items_from_props
      .select { |link| set_items.include?(link[:type]) }
      .sort_by { |link| set_items.index(link[:type]) }
      .map do |link|
        item_name =
          if link[:type] == :questions # sigh..
            I18n.translate("navigation.questions")
          else
            link[:name] || link[:englishText] || ""
          end
        {
          text: item_name,
          href: link[:path],
          active: link[:type] == ReportDataTypes::ACTIVITY
        }
      end
  end

  def construct_activity_report_title_block_props
    set_locale(survey_topic.survey) if survey_topic
    secondary_actions = []
    if lifecycle_admin && can_administer_survey
      secondary_actions << {
        label: I18n.t("reports.titleblock.actions.configure"),
        iconType: "configure",
        href: get_survey_configure_path
      }
    end
    if can_administer_account && lifecycle_admin
      secondary_actions << {
        label: I18n.t("reports.lifecycle.actions.add_user"),
        iconType: "userAdd",
        actionType: "open-user-add-edit-drawer",
        userAddEditDrawerPath: "add?active_account_id=#{account.aggregate_id}&originating-page=account-users"
      }
    end
    {
      title: I18n.t("reports.titleblock.title") + ": " + survey.name,
      textDirection: i18n_text_direction,
      breadcrumb: {
        text: I18n.t("reports.titleblock.back_to_text"),
        path: account_home_reports_index_path(account)
      },
      rawSecondaryActions: secondary_actions,
      rawNavigationTabs: title_block_nav_items
    }.merge(survey.accepting_responses? && lifecycle_admin ? {
      rawPrimaryAction: {
        label: I18n.t("reports.lifecycle.actions.select_employees"),
        iconType: "forwardArrow",
        className: "add-exit-response"
      }
    } : {})
  end

  def construct_response_detail_props(employee_name:)
    {
      title: I18n.t("reports.lifecycle.response_detail.#{survey.type.downcase}_title") + ": " + employee_name,
      textDirection: i18n_text_direction,
      breadcrumb: {
        text: I18n.t("reports.lifecycle.response_detail.back_to_text"),
        path: Navigation::LifecyclePaths.new(survey, current_user).index
      },
      rawSecondaryActions: [
        {
          label: I18n.t("reports.titleblock.actions.export"),
          rawMenuItems: action_props.to_hash[:actionLinksList].map do |action_prop|
            temp_action_prop = action_prop
            if action_prop[:id] == "raw_data_export_action"
              temp_action_prop[:url] = "#raw_data_extract_legacy_exit_controller"
            end
            temp_action_prop
          end
        }
      ]
    }
  end

  def paginated_number_of_person
    account.influx? ? 5 : 7
  end

  def valid_sections_for_survey(survey)
    sections = [:response]
    sections << :interview if survey.full_workflow?
    sections << :classify if survey.lifecycle_close_step?
    sections
  end

  def survey_in_context
    if params[:survey_id].present?
      Survey.find(params[:survey_id])
    elsif params[:exit_response_id].present?
      Response.find(params[:exit_response_id]).survey
    elsif params[:id].present?
      survey_topic.survey
    end
  end

  def account_in_context
    survey.try(:account)
  end

  def paginated_responses
    responses_in_context.paginate(page: params[:page], per_page: 50)
  end

  def responses_in_context
    load_topics(params)
  end

  def unfiltered_responses
    load_topics({})
  end

  def load_topics(options)
    page_data = Lifecycle::OnboardAndExit::PageData.new(survey: survey, user: current_user)
    if current_user.can_administer_lifecycle?(survey)
      page_data.all_topics(options)
    else
      page_data.reviewer_topics(options)
    end
  end

  def people_in_context
    people = people_active_until(Date.today)

    if params[:type] != "reviewer-step"
      people = demographically_filtered_people(people)
    end

    if params[:term]
      people = people.with_search_term(params[:term])
      params[:type] == "employee-step" ? people.employees : people
    else
      people_when_type_not_reviewer_step(people)
    end
  end

  def people_active_until(active_until_date)
    if params[:type] == "employee-step" && survey.exit?
      active_until_date -= survey.config(Configs::DAYS_ELIGABLE_AFTER_EXIT).days
    end

    account.people.associated.active(active_until_date).order_by(:name_sort.asc).limit(paginated_number_of_person)
  end

  def people_when_type_not_reviewer_step(people)
    results = []
    people.each do |person|
      if person.employee? && Response.where(survey: survey, user: person).none?
        results << person
        break if results.length == paginated_number_of_person
      end
    end
    results
  end

  def survey_topic_in_context
    @topic ||= params[:id] ? load_existing_survey_topic(params[:id]) : new_topic
  end

  def load_existing_survey_topic(id)
    SurveyTopic.find(id)
  end

  def new_topic
    topic = SurveyTopic.new(survey: survey)
    topic.issue
    topic
  end

  def set_survey_topic_params
    survey_topic.subject = employee
    survey_topic.send_survey_to = param_to_sym(:send_survey_to, SurveyTopic::VALID_SEND_SURVEY_TO_TYPES, :employee)
    survey_topic.survey_due_date = DateMethods.extract_date_from_param(params[:survey_due]) unless survey_topic.send_survey_to == :no_one

    # Basic workflow doesn't ask about interview_due, so derive it if missing...
    # (It could be needed if basic is flipped back to full)
    survey_topic.interview_due_date = DateMethods.extract_date_from_param(params[:interview_due], survey.lifecycle_interview_due_date)
    survey_topic.interviewer = reviewer if survey.full_workflow?
  end

  def valid_params?(last_day_of_employment:)
    present?(survey_topic.subject, survey_topic.interview_due_date) &&
      (last_day_of_employment.present? || !survey.exit?) &&
      (survey_topic.interviewer.present? || !survey.full_workflow?) &&
      (survey_topic.survey_due_date.present? || survey_topic.send_survey_to == :no_one) &&
      # Can't start if an non-deleted exit already exists for this survey
      survey.responses.where(user_id: employee.id).not_in(status: [:deleted, :closed]).first_by_id.blank? &&
      survey.survey_topics.where(subject_id: employee.id).not_in(status: [:deleted, :closed]).first_by_id.blank?
  end

  def report_access_grant_finder
    ReportAccessGrantFinder.new(current_user, survey.id, params[:report_id], params[:a])
  end

  def on_main_page
    params[:survey_id].present?
  end

  def construct_header_props
    base_props = if on_main_page
      all_survey_topics_props
    else
      specific_survey_topic_props
    end

    base_props.merge(
      actionLinks: action_props,
      backLinkLabel: "Back",
      backLinkPath: Navigation::LifecyclePaths.new(
        survey,
        current_user
      ).index,
      showNav: !on_main_page
    )
  end

  def all_survey_topics_props
    Props::HeaderProps.new(
      survey,
      current_user,
      paths,
      Navigation::SurveyLinks.new(survey: survey, user: current_user).configure,
      report_type,
      questions_for_reports.has_multiple_choice_questions?,
      allow_leader_selection?,
      leader_selection_applies?,
      params[:report_id] || "admin",
      reports: report_access_props,
      actionLinks: Props::ActivityReportActionProps.new
    ).to_hash
  end

  def specific_survey_topic_props
    Props::ExitResponseHeaderProps.new(
      context,
      paths,
      survey_topic.subject_response,
      current_user,
      @section,
      actionLinks: action_props
    ).to_hash
  end

  def select_manager
    return unless survey.full_workflow?

    old_interviewer = Person.find(survey_topic.interviewer.id) if survey_topic.interviewer
    new_interviewer = Person.find_by(aggregate_id: params[:manager_id])
    return if new_interviewer == old_interviewer

    survey_topic.interviewer = new_interviewer
    survey_topic.save!
    survey_topic.subject_response.reviewer = new_interviewer
    survey_topic.subject_response.save!
    Lifecycle::LifecycleCommunications.new.reviewer_updated(survey_topic, old_interviewer, current_user)
    # if the reviewer has never been sent a login invite, send them one now!
    survey_topic.interviewer.notify_new_account unless new_interviewer.invited?
  end

  def classify
    params[:question_ids].split(",").each do |id|
      a = survey_topic.subject_response.answers.where(question_id: id).first_by_id
      next if a.blank?

      option_id = params[id]
      q = Question.find(id)
      q.active_select_options.detect { |so| so.id.to_s == option_id }.try do |select_option|
        a.select_option_ids = [select_option.id]
      end
    end
    survey_topic.subject_response.save!
  end

  def three_sixty_paths
    Navigation::ThreeSixtyPaths.new(survey, current_user)
  end

  def process_actions(process)
    @process_actions ||= Lifecycle::ProcessActions.new(current_user, survey)
    @process_actions.for(process)
  end

  def deactivate_employee(employee, last_day_of_employment)
    if account.influx?
      employee_params = {
        name: employee.name,
        preferred_name: employee.preferred_name,
        employee_id: employee.employee_id,
        email: employee.email,
        date_of_birth: employee.date_of_birth,
        start_date: employee.start_date&.iso8601,
        end_date: last_day_of_employment.iso8601,
        locale: employee.locale,
        observer: employee.association_type == :advisorship
      }

      employee_service.update(
        employee.aggregate_id,
        employee_params,
        account.aggregate_id,
        current_user.aggregate_id
      )
    else
      employee.deactivate!(last_day: last_day_of_employment)
      []
    end
  end

  def save_user_focus
    return if survey.blank? || current_user.nil?

    UserFocus.save_focus(user: current_user, survey: survey, request: request)
  end

  def demographically_filtered_people(people)
    Services::PeopleFilteredByDemographic.new(
      survey: survey,
      people: people
    ).call
  end

  def construct_total_no_of_people
    demographically_filtered_people(account.people.associated.employees.active).count
  end

  def set_locale(survey)
    super(RequestLocale.locale(
      configurable: survey,
      person: current_user,
      request: request
    ))
  end
end
