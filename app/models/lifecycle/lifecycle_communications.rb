module Lifecycle
  class LifecycleCommunications
    attr_reader :deliver

    def initialize(send_communications: nil)
      send_communications ||= ::Communications::SendCommunications.new(
        slack_communication_variables: ::Communications::Slack::LifecycleCommunicationVariables.new,
        ms_teams_communication_variables: ::Communications::Email::LifecycleAndThreeSixtyCommunicationVariables.new
      )
      @deliver = Deliver.new(send_communications: send_communications)
    end

    def feedback_collected(process)
      return if process.survey.account.churned? || process.awaiting_responses?

      deliver.all(to_reviewer(process, "survey_complete_reviewer", whenever: reviewer_is_present))
    end

    def collection_reminders_sent(process)
      return if process.survey.account.churned? || !process.awaiting_responses?

      if process.send_survey_to == :reviewer
        deliver.all(to_reviewer(process, "survey_reminder_on_employee_behalf", whenever: reviewer_is_present))
      else
        deliver.all(to_user(process.subject_response, "survey_reminder"))
      end
    end

    def collection_reminders_due(process)
      return if process.survey.account.churned? || !process.awaiting_responses?

      comms = if process.send_survey_to == :reviewer
        to_reviewer(process, "survey_reminder_on_employee_behalf", whenever: reviewer_is_present)
      else
        to_user(process.subject_response, "survey_reminder")
      end

      deliver.only_once(comms, tracking_reminder_status: :response_reminder)
    end

    def interview_reminders_sent(process)
      return if process.survey.account.churned? || process.awaiting_responses?

      deliver.all(
        to_reviewer(process, "interview_reminder", whenever: reviewer_is_present)
      )
    end

    def interview_reminders_due(process)
      return if process.survey.account.churned? || process.awaiting_responses?

      deliver.only_once(
        to_reviewer(process, "interview_reminder", whenever: reviewer_is_present),
        tracking_reminder_status: :complete_process
      )
    end

    def due_date_passed(_)
    end

    def process_updated(_, _)
    end

    def process_created(process, correlation_id)
      return if process.survey.account.churned?

      deliver.all(
        to_user(process.subject_response, "invite_user", correlation_id: correlation_id, whenever: configured_to_send_to_user(process.send_survey_to)),
        to_reviewer(process, invite_reviewer_template(process), correlation_id: correlation_id, whenever: reviewer_is_present)
      )
    end

    def reviewer_updated(process, previous_reviewer, admin)
      return if process.survey.account.churned?

      deliver.all(
        to_reviewer(process, "switch_reviewer"),
        to_previous_reviewer(
          process,
          previous_reviewer,
          "switch_reviewer_previous",
          additional_fields: lambda do |previous|
            {
              previous_reviewer_name: previous.name,
              previous_reviewer_preferred_name: previous.display_name,
              admin_person_email: admin.email
            }
          end
        )
      )
    end

    def response_resent(response)
      return if response.survey.account.churned?

      deliver.all(to_user(response, "invite_user", whenever: configured_to_send_to_user(response.send_survey_to)))
    end

    def response_reset(response)
      return if response.survey.account.churned?

      deliver.all(to_user(response, "invite_user", whenever: configured_to_send_to_user(response.send_survey_to)))
    end

    private

    def invite_reviewer_template(process)
      {
        no_one: "invite_reviewer_skipped",
        reviewer: "invite_reviewer_on_employee_behalf"
      }.fetch(process.send_survey_to, "invite_reviewer")
    end

    def to_user(response, template, correlation_id: nil, whenever: Predicate.always)
      PossibleCommunication.new(
        should_send: whenever.call(response),
        communication: lambda do
          ::Communications::CommunicationConfiguration.new(
            response: response,
            conversation: response.conversation(:participant),
            template_type: template,
            recipient: response.user,
            correlation_id: correlation_id,
            additional_fields: {}
          )
        end
      )
    end

    def to_reviewer(process, template, correlation_id: nil, whenever: Predicate.always)
      PossibleCommunication.new(
        should_send: whenever.call(process.subject_response),
        communication: lambda do
          ::Communications::CommunicationConfiguration.new(
            response: process.subject_response,
            conversation: process.subject_response.conversation(:reviewer),
            template_type: template,
            recipient: process.subject_response.reviewer,
            correlation_id: correlation_id,
            additional_fields: {}
          )
        end
      )
    end

    def to_previous_reviewer(process, previous_reviewer, template, whenever: Predicate.always, additional_fields:)
      PossibleCommunication.new(
        should_send: whenever.call(process.subject_response),
        communication: lambda do
          ::Communications::CommunicationConfiguration.new(
            response: process.subject_response,
            conversation: process.subject_response.conversation(:reviewer),
            template_type: template,
            recipient: previous_reviewer,
            additional_fields: additional_fields.call(previous_reviewer)
          )
        end
      )
    end

    def reviewer_is_present
      ->(response) { response.reviewer.present? }
    end

    def configured_to_send_to_user(send_survey_to)
      ->(_) { send_survey_to == :employee }
    end

    def new_reviewer(diff)
      ->(_) { diff.new_interviewer.present? }
    end

    def reviewer_switched(diff)
      ->(_) { diff.previous_interviewer.present? }
    end
  end
end
