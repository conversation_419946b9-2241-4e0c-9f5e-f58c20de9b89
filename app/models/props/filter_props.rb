class Props::FilterProps
  private_class_method :new

  def self.from_context(context, date_range, paths, level_from_filter: Reporting::LeaderSelection::LevelFromFilter)
    new(
      survey: context.survey,
      paths: paths,
      date_range: date_range,
      time_zone: context.account.time_zone,
      anchor: context.anchor,
      anchors: context.anchors || [],
      filters: context.filters(false),
      dissect_questions: context.dissect_questions,
      hierarchy_question: context.hierarchy_question,
      leader_parameter: context.leader_parameter,
      leader_filter: context.leader_filter,
      level_from_filter: level_from_filter
    )
  end

  def to_hash
    {
      "dateSummary" => date_summary,
      "dateFilters" => date_filters_prop,
      "demographics" => sorted_demographics,
      "filtersUrl" => paths&.filter_path,
      "anchor" => anchor_prop(anchor),
      "anchors" => anchors.map(&method(:anchor_prop)),
      "demographicFilters" => selected_filters,
      "maxFilters" => survey.config(Configs::MAXIMUM_REPORT_FILTERS),
      "hierarchy" => hierarchy?,
      "uiText" => ui_text,
      "surveyPeriodType" => survey.survey_period_type,
      "selectedLeader" => {
        id: @leader_parameter,
        level: @level_from_filter.new(leader_filter: @leader_filter).call
      },
      "removeUnreportableDemographicValuesFromFilterFlag" => survey.remove_unreportable_demographic_values_from_filter_enabled?
    }
  end

  def to_date_filter
    date_filter = date_filters_prop
    date_filter.present? ? date_filter.except(:from_date, :to_date) : nil
  end

  def survey_reporting_api_date_filter
    date_filter = api_date_filters_prop
    date_filter.present? ? date_filter.except(:from_date, :to_date) : nil
  end

  private

  attr_reader :survey, :paths, :date_range, :time_zone, :anchor, :anchors, :filters, :dissect_questions, :hierarchy_question

  def initialize(
    survey:,
    paths:,
    date_range:,
    time_zone:,
    anchor:,
    anchors:,
    filters:,
    dissect_questions:,
    hierarchy_question:,
    leader_parameter:,
    leader_filter:,
    level_from_filter:,
    filter_module: ReportFilters::FilterModule.new
  )
    @survey = survey
    @paths = paths
    @date_range = date_range
    @time_zone = time_zone
    @anchor = anchor
    @anchors = anchors
    @filters = filters
    @dissect_questions = dissect_questions
    @hierarchy_question = hierarchy_question
    @leader_parameter = leader_parameter
    @leader_filter = leader_filter
    @level_from_filter = level_from_filter
    @filter_module = filter_module
  end

  def date_summary
    {
      dateRange: render_date_summary,
      timeRange: "Current Results"
    }
  end

  def render_date_summary
    from = date_range.from
    to = date_range.to
    return nil unless from != date_range.default_from || to != date_range.default_to

    "#{from.strftime("%d %b %Y")} to #{to.strftime("%d %b %Y")}"
  end

  def date_filters_prop
    return nil unless @survey.lifecycle? || @survey.adhoc?

    {
      from_date: format_datefilters_prop_date(@date_range.from),
      default_from_date: format_datefilters_prop_date(@date_range.default_from),
      default_to_date: format_datefilters_prop_date(@date_range.default_to),
      to_date: format_datefilters_prop_date(@date_range.to),
      max_date: format_datefilters_prop_date(@time_zone.now)
    }
  end

  def api_date_filters_prop
    return nil unless @survey.lifecycle? || @survey.adhoc?

    {
      defaultFromDate: format_datefilters_prop_date(@date_range.default_from),
      defaultToDate: format_datefilters_prop_date(@date_range.default_to),
      maxDate: format_datefilters_prop_date(@time_zone.now)
    }
  end

  def anchor_prop(anchor)
    return {} if anchor.blank?

    {
      id: anchor.to_s,
      text: anchor.display_name,
      hierarchy: anchor.hierarchy?,
      level: @level_from_filter.new(leader_filter: anchor).call
    }
  end

  def format_datefilters_prop_date(date)
    return unless date

    date.strftime("%d %b %Y")
  end

  def sorted_demographics
    sorted_demographics = DemographicFilters.new(dissect_questions, hierarchy_question).sorted_demographics
    return nil if sorted_demographics.nil?
    sorted_demographics.map do |demographic|
      {
        id: demographic[:id],
        name: demographic[:name],
        filterModule: demographic[:filter_module]
      }
    end
  end

  def hierarchy?
    hierarchy_question.present?
  end

  def selected_filters
    filters.map do |filter|
      stq = filter.survey_to_question
      {
        demographic_id: stq.id.to_s,
        demographic_label: stq.question.name,
        filterModule: @filter_module.call(stq: stq),
        group_id: filter.to_s,
        group_label: filter.label
      }
    end
  end

  def ui_text
    {
      addFilter: I18n.t("common.filters.add_filter"),
      clearFilter: I18n.t("common.filters.clear_all"),
      viewReportFor: I18n.t("common.filters.view_report_for"),
      noFilters: I18n.t("common.filters.no_filters")
    }
  end
end
