require "rails_helper"

RSpec.describe Jobs::ScheduleLifecycleInvites do
  let(:account) { FactoryBot.create(:account_without_users) }

  describe "#days_from_onboard_exit" do
    let(:job) { Jobs::ScheduleLifecycleInvites.create!(survey: survey, account: account, status: :hold) }

    context "when a background job is created for an exit survey" do
      let(:survey) { Survey.create(type: :exit) }
      it "sends invites default days before the last day of employment" do
        expect(job.days_from_onboard_exit).to eq(Jobs::ScheduleLifecycleInvites::DEFAULT_DAYS_BEFORE_EXIT_TO_TRIGGER_INVITE)
      end
    end

    context "when a background job is created for an onboard survey" do
      let(:survey) { Survey.create(type: :onboard) }
      it "sends invites default days after the first day of employment" do
        expect(job.days_from_onboard_exit).to eq(Jobs::ScheduleLifecycleInvites::DEFAULT_MINIMUM_DAYS_SINCE_ONBOARDING_TO_TRIGGER_INVITE)
      end
    end

    context "when the survey for the job has been deleted" do
      let(:survey) { nil } # faking a deleted survey since creating one here keeps it in memory and a date will be calculated

      it "should return the default value for days_from_onboard_exit" do
        expect(job.days_from_onboard_exit).to eq(Jobs::ScheduleLifecycleInvites::DEFAULT_DAYS_BEFORE_EXIT_TO_TRIGGER_INVITE)
      end
    end
  end

  describe "#maximum_days_since_start_date" do
    context "when a background job is created for an onboard survey without a maximum_days_since_start_date" do
      let(:survey) { Survey.create(type: :onboard) }
      let(:days_from_onboard_exit) { 22 }
      let(:job) { Jobs::ScheduleLifecycleInvites.create!(survey: survey, account: account, status: :hold, days_from_onboard_exit: days_from_onboard_exit) }
      it "sets defaults maximum_days_since_start_date following the DEFAULT_MAXIMUM_WINDOW_TO_TRIGGER_INVITE" do
        expect(job.maximum_days_since_start_date).to eq(days_from_onboard_exit + Jobs::ScheduleLifecycleInvites::DEFAULT_MINIMUM_DAYS_SINCE_ONBOARDING_TO_TRIGGER_INVITE)
      end
    end

    context "when the survey for the job has been deleted" do
      let(:survey) { nil } # faking a deleted survey since creating one here keeps it in memory and a date will be calculated
      let(:days_from_onboard_exit) { 22 }
      let(:job) { Jobs::ScheduleLifecycleInvites.create!(survey: survey, account: account, status: :hold, days_from_onboard_exit: days_from_onboard_exit) }

      it "sets defaults maximum_days_since_start_date following the DEFAULT_MAXIMUM_WINDOW_TO_TRIGGER_INVITE" do
        expect(job.maximum_days_since_start_date).to eq(nil)
      end
    end
  end

  describe "#reschedule", delay_jobs: true do
    let(:survey) { Survey.create!(name: "Exit survey", type: :exit) }
    let(:job) { Jobs::ScheduleLifecycleInvites.create!(account: account, survey: survey, status: :hold) }
    let(:rescheduled_job) { Jobs::ScheduleLifecycleInvites.find_by(survey_id: survey.id, status: :new) }

    before do
      job.run
      job.reschedule
    end

    it "creates a new rescheduled_job" do
      expect(rescheduled_job.id).not_to eq(job.id)
    end

    it "retains the attributes of the original job" do
      expect(rescheduled_job.days_from_onboard_exit).to eq(job.days_from_onboard_exit)
    end

    it "has an empty participants array" do
      expect(rescheduled_job.participants).to eq []
    end
  end

  describe "#run" do
    let(:already_invited) { false }

    let(:survey) do
      FactoryBot.create(
        :basic_survey,
        type: :onboard,
        account: account,
        status: survey_status,
        archived: survey_archived,
        flags: {"trigger_lifecycle_invites" => "enabled"}
      )
    end
    before { allow(survey).to receive(:full_workflow?).and_return(false) }

    let!(:associated_users) { FactoryBot.create_list(:user, 2, account: account, start_date: Date.today - 7) }
    let(:job) { Jobs::ScheduleLifecycleInvites.create!(survey: survey, account: account, status: :hold, days_from_onboard_exit: 7) }
    let(:start_lifecycle_process) { instance_double(Surveys::Commands::StartLifecycleProcess) }

    before do
      allow(Surveys::Commands::StartLifecycleProcess).to receive(:new).and_return(start_lifecycle_process)
      allow(start_lifecycle_process).to receive(:call)
    end

    shared_examples "lifecycle process are not started" do
      it "does not start any lifecycle process" do
        expect(start_lifecycle_process).to_not receive(:call)
        job.run
      end
    end

    context "inactive surveys" do
      context "if the survey has not yet been started" do
        let(:survey_status) { :design }
        let(:survey_archived) { false }
        it_behaves_like "lifecycle process are not started"
      end

      context "if the survey has been closed" do
        let(:survey_status) { :closed }
        let(:survey_archived) { false }
        it_behaves_like "lifecycle process are not started"
      end

      context "if the survey has been archived" do
        let(:survey_status) { :active }
        let(:survey_archived) { true }
        it_behaves_like "lifecycle process are not started"
      end
    end

    context "deleted survey" do
      let(:survey_status) { :active }
      let(:survey_archived) { false }

      before { survey.destroy }

      it_behaves_like "lifecycle process are not started"
    end

    context "account is churned" do
      let(:survey_status) { :active }
      let(:survey_archived) { false }

      before do
        account.status = :churned
        account.save!
        job.run
      end

      it_behaves_like "lifecycle process are not started"

      it "has a status of pending" do
        expect(job.reload.status).to eq :pending
      end
    end

    context "full workflow survey mis-configured to issue automated invitations (not supported)" do
      let(:survey_status) { :active }
      let(:survey_archived) { false }
      before { allow(survey).to receive(:full_workflow?).and_return(true) }

      it_behaves_like "lifecycle process are not started"
    end

    context "active survey" do
      let(:survey_status) { :active }
      let(:survey_archived) { false }

      context "when survey contains invalid participants" do
        let(:valid_user) { associated_users[0] }
        let(:invalid_user) { associated_users[1] }
        before do
          invalid_user.name = nil
          invalid_user.save(validate: false)
        end

        it "does not throw an exception" do
          expect { job.run }.to_not raise_error
        end

        it "filters the invalid participants and send an email notification" do
          expect(SurveyInviteFailedMailer)
            .to receive(:delay)
            .and_return(SurveyInviteFailedMailer)
          expect(SurveyInviteFailedMailer)
            .to receive(:generate)
            .with(account.name, survey.name, job.id, [invalid_user.id])

          job.run

          expect(job.participants).to eq [valid_user]
          expect(job.invalid_participant_ids).to eq [invalid_user.id]
        end
      end

      it "starts lifecycle process for all newbies" do
        expect(start_lifecycle_process).to receive(:call).twice
        job.run
      end

      context "when an observer is added" do
        let(:advisor) { FactoryBot.create(:advisor, account: account, start_date: Date.today - 7) }

        it "does not invite the advisor" do
          expect(job).to_not receive(:invite_person).with(advisor, anything, anything)
          job.run
        end
      end

      context "when the newbies have already been invited" do
        before do
          associated_users.each { |user| survey.responses.create!(user: user) }
        end

        it_behaves_like "lifecycle process are not started"
      end

      context "when the survey has eligibility flag" do
        let(:eligibility_question) { FactoryBot.create(:yes_no_question) }
        let(:eligibility_stq) { SurveyToQuestion.new(question: eligibility_question, type: :segment) }

        before do
          MasterSurvey.create!(account: account, name: "Master", type: :master)
          add_eligibility_flag(survey)
          assign_eligibility_flag(associated_users.first)
        end

        it "starts lifecycle process for eligible newbie only" do
          expect(start_lifecycle_process).to receive(:call).once
          job.run
        end
      end

      describe "when survey has RANDOM_DEMOGRAPHIC_CODE" do
        let(:random_demographic_question) { FactoryBot.create(:yes_no_question) }
        let(:random_demographic_stq) { SurveyToQuestion.new(question: random_demographic_question, type: :classify) }
        let(:demographic_id) { random_demographic_question.aggregate_id }

        before do
          MasterSurvey.create!(account: account, name: "Master", type: :master)
        end

        context "set up" do
          before do
            assign_random_demographic_question_code(survey, random_demographic_stq)
          end

          it "assigns a random demographic value when inviting user" do
            job.run
            associated_users.each(&:reload)
            expect(associated_users.first.demographic_value(demographic_id)).to be_in(random_demographic_question.select_options.all.map(&:demographic_value_id))
          end
        end

        context "disabled" do
          it "does not assign demographic value when inviting user" do
            job.run
            associated_users.each(&:reload)
            expect(associated_users.first.demographic_value(demographic_id)).to be_nil
          end
        end
      end
    end
  end

  def assign_eligibility_flag(person)
    demographic_value = eligibility_question.select_options.find_by(value: "Yes")
    person.set_demographic_value!(eligibility_question.aggregate_id, demographic_value.demographic_value_id)
  end

  def add_eligibility_flag(survey)
    survey.assign_config(Configs::TRIGGER_ELIGIBILITY_FLAG, eligibility_question.code)
    survey.survey_to_questions << eligibility_stq
    survey.save!
  end

  def assign_random_demographic_question_code(survey, stq)
    survey.assign_config!(Configs::RANDOM_DEMOGRAPHIC_CODE, stq.question.code)
    survey.survey_to_questions << stq
    survey.save!
  end

  describe "#error_hook" do
    let(:survey) { Survey.create }
    let(:job) { Jobs::ScheduleLifecycleInvites.create!(account: account, survey: survey, status: :error, run_at: Time.now) }

    it "reschedules on error" do
      expect(job).to receive(:reschedule).with(job.run_at + 1.day)
      job.error_hook
    end

    context "when same job already scheduled" do
      before { Jobs::ScheduleLifecycleInvites.create!(account: account, survey: survey, status: :hold, run_at: Time.now + 1.day) }

      it "does not reschedule" do
        expect(job).not_to receive(:reschedule).with(job.run_at + 1.day)
        job.error_hook
      end
    end
  end

  describe "#participant_count" do
    let(:job) do
      Jobs::ScheduleLifecycleInvites.create!(
        account: account,
        survey: survey,
        status: :hold,
        days_from_onboard_exit: 7,
        baseline: Date.today - 2.years
      )
    end

    subject { job.participant_count }

    context "when survey type is onboard" do
      let(:survey) do
        FactoryBot.create(
          :basic_survey,
          type: :onboard,
          account: account
        )
      end

      context "when users have not reach days_from_onboard_exit days" do
        before { FactoryBot.create(:user, account: account, start_date: Date.today - 6.days) }
        it { expect(subject).to eq 0 }
      end

      context "when users started before the baseline date" do
        before { FactoryBot.create(:user, account: account, start_date: Date.today - 3.years) }
        it { expect(subject).to eq 0 }
      end

      context "when users started after the baseline date and after days_from_onboard_exit days" do
        before do
          FactoryBot.create(:user, account: account, start_date: Date.today - 7.days)
          FactoryBot.create(:user, account: account, start_date: Date.today - 2.weeks)
        end

        it { expect(subject).to eq 2 }
      end
    end
  end
end
