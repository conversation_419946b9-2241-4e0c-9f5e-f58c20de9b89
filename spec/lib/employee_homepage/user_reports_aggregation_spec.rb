require "rails_helper"
require "spec_access_helper"

module EmployeeHomepage
  RSpec.describe UserReportsAggregation do
    let(:account) { Account.create!(name: "blaze inc", subdomain: "blazeinc", id: "1234", region: "APAC") }
    let(:user) { Person.create!(account: account, name: "Bobby Tables", password: "password") }
    let(:user2) { Person.create!(account: account, name: "Bobby Chairs", password: "password") }
    subject(:reports) { UserReportsAggregation.new(user: user, account: account).all_reports }

    context "fetching surveys via the aggregation pipeline," do
      context "when there is an engagement survey and a hierarchy survey that are not deleted," do
        let(:question) {
          question = SingleSelectQuestion.create!(
            name: "office"
          )
          select_option = SelectOption.create!(employee_aggregate_id: "1", value: "Ceo", select_question: question)
          question.select_options = [select_option]
          question
        }
        let(:question2) {
          question2 = SingleSelectQuestion.create!(
            name: "office"
          )
          select_option = SelectOption.create!(employee_aggregate_id: "1", value: "Ceo", select_question: question2)
          select_option2 = SelectOption.create!(employee_aggregate_id: "3", value: "Smurf", select_question: question2)
          question2.select_options = [select_option, select_option2]
          question2
        }
        let(:question3) {
          question3 = SingleSelectQuestion.create!(
            name: "office"
          )
          select_option = SelectOption.create!(employee_aggregate_id: "1", value: "Ceo", select_question: question3)
          question3.select_options = [select_option]
          question3
        }

        let(:survey) {
          survey = Survey.create!(account: account, status: :active, type: :engagement, name: "My Cool Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47))
          survey.survey_to_questions.create!(
            question_id: question.id,
            status: :active
          )
          survey.survey_to_questions.create!(
            question_id: question2.id,
            status: :active
          )
          survey.survey_to_questions.create!(
            question_id: question3.id,
            status: :active
          )
          survey
        }
        let(:hierarchy_question) {
          hierarchy_question = SingleSelectQuestion.create!(
            account: account,
            hierarchy_type: :employee,
            hierarchy_max_empty_fields: 1
          )
          select_option1 = SelectOption.create!(employee_aggregate_id: "1", value: "Ceo", select_question: hierarchy_question)
          select_option2 = SelectOption.create!(employee_aggregate_id: "2", value: "Middle Manager", select_question: hierarchy_question)
          select_option3 = SelectOption.create!(employee_aggregate_id: "3", value: "Bottom", select_question: hierarchy_question)
          hierarchy_question.select_options = [select_option1, select_option2, select_option3]
          hierarchy_question
        }
        let(:hierarchy) {
          ceo_option, middle_manager_option, bottom_manager_option = hierarchy_question.select_options
          Reporting::EmployeeHierarchy.create!(
            hierarchy_question: hierarchy_question,
            select_option_to_parent: {
              bottom_manager_option.id.to_s => middle_manager_option.id.to_s,
              middle_manager_option.id.to_s => ceo_option.id.to_s,
              ceo_option.id.to_s => nil
            },
            demographic_value_to_parent: {
            }
          )
        }
        let(:hierarchy_survey) {
          survey = Survey.create!(account: account, status: :active, type: :engagement, name: "My Cool Hierarchy Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47))

          survey.survey_to_questions.create!(
            question_id: hierarchy_question.id,
            status: :active,
            employee_hierarchy: hierarchy
          )
          hierarchy.survey_id = survey.id
          survey
        }
        context "when the report is a multidemographic report with report_scope," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false, base_demographic_stq_id: nil, report_scope: {survey.stqs.first.id.to_s => [survey.stqs.first.question.select_options.first.id], survey.stqs[1].id.to_s => [survey.stqs[1].question.select_options.first.id], survey.stqs[2].id.to_s => [survey.stqs[2].question.select_options.first.id]}) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER, select_option_ids: [survey.stqs.first.question.select_options.first.id, survey.stqs[1].question.select_options.first.id, survey.stqs[2].question.select_options.first.id]) }

            it "doesn't include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
        end

        context "when the report is not a hierarchy report," do
          context "when the report doesn't have a base_demographic_stq_id," do
            let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false) }
            context "when a report grant exists for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

              it "includes reports with viewer access" do
                rag.touch
                expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}, access: "viewer"))
              end
            end
            context "when a report grant doesn't exist for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user2, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

              it "doesn't include reports" do
                rag.touch
                expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
              end
            end
            context "when a report grant exist but is deleted for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER, deleted_at: DateTime.new(2018, 1, 3, 16, 47)) }

              it "doesn't include reports" do
                rag.touch
                expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
              end
            end
            context "when a response exists where the user is a reviewer," do
              let(:response) { Response.create!(reviewer: user, survey: survey) }

              it "doesn't includes reports" do
                response.touch
                expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
              end
            end
          end
          context "when the report view is summary," do
            let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false, report_view: "summary") }
            context "when a report grant exists for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

              it "includes reports and includes access as summary" do
                rag.touch
                expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}, access: "summary"))
              end
            end
          end
          context "when the report view is participation," do
            let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false, report_view: "participation") }
            context "when a report grant exists for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

              it "includes reports and includes access as participation" do
                rag.touch
                expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}, access: "participation"))
              end
            end
          end
          context "when the there are two reports with different views with access to the same survey," do
            let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false, report_view: "participation") }
            let(:report2) { Report.create!(survey: survey, name: "Report2", sharing_status: "published", is_hierarchy_report: false, report_view: "advanced") }
            context "when a report grant exists for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report2, action_framework_role: ReportAccessGrant::VIEWER) }

              it "includes reports and includes access as viewer" do
                rag.touch
                expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}, access: "viewer", reportId: report2.id))
              end
            end
          end
          context "when the report has a base_demographic_stq_id," do
            let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false, base_demographic_stq_id: survey.stqs.first.id) }
            context "when a report grant exists for the current user," do
              context "and it has a select_option_id," do
                let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER, select_option_id: survey.stqs.first.question.select_options.first.id) }

                it "includes reports" do
                  rag.touch
                  expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}))
                end
              end
              context "but it doesn't have a select_option_id," do
                let(:rag) { ReportAccessGrant.create(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

                it "doesn't include reports" do
                  rag.touch
                  expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
                end
              end
            end
            context "when a report grant doesn't exist for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user2, report: report, action_framework_role: ReportAccessGrant::VIEWER, select_option_id: survey.stqs.first.question.select_options.first.id) }

              it "doesn't include reports" do
                rag.touch
                expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
              end
            end
            context "when a report grant exist but is deleted for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER, deleted_at: DateTime.new(2018, 1, 3, 16, 47), select_option_id: survey.stqs.first.question.select_options.first.id) }

              it "doesn't include reports" do
                rag.touch
                expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
              end
            end
          end
        end

        context "when the report is a hierarchy report, where the base_demographic_stq_id matches a stq on the survey" do
          let(:hierarchy_report) {
            Report.create!(survey: hierarchy_survey, name: "Report", sharing_status: "published", is_hierarchy_report: true, base_demographic_stq_id: hierarchy_survey.stqs.first.id, hierarchy_level: 0, hierarchy_levels: [0])
          }
          context "when a report grant exists for the current user with a matching select_option," do
            let(:rag) {
              ReportAccessGrant.create!(report_consumer: user, report: hierarchy_report, action_framework_role: ReportAccessGrant::VIEWER, select_option_id: hierarchy_survey.stqs.first.question.select_options.first.id)
            }

            it "includes reports" do
              rag.touch
              expect(reports[:reports]).to include(hash_including(surveyName: {en: hierarchy_survey.name}))
            end
          end
          context "when a report grant exists for the current user without a matching select_option," do
            let(:rag) {
              ReportAccessGrant.create(report_consumer: user, report: hierarchy_report, action_framework_role: ReportAccessGrant::VIEWER, select_option_id: hierarchy_survey.stqs.first.question.select_options[1].id)
            }

            it "doesn't include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: hierarchy_survey.name}))
            end
          end
          context "when a report doesn't exist for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user2, report: hierarchy_report, action_framework_role: ReportAccessGrant::VIEWER, select_option_id: hierarchy_survey.stqs.first.question.select_options.first.id) }

            it "doesn't include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: hierarchy_survey.name}))
            end
          end
        end

        context "when the report is not published," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "unpublished", is_hierarchy_report: false) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "does not include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
          context "when the user is a HRBP," do
            before do
              allow(user).to receive(:hr_business_partner?).and_return(true)
            end

            context "when a report grant exists for the current user," do
              let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

              it "does not include reports" do
                rag.touch
                expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}))
              end
            end
          end
        end

        context "when the report is deleted," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "unpublished", is_hierarchy_report: false, deleted_at: DateTime.new(2018, 1, 3, 16, 47)) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "does not include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
        end
      end
      context "when there is an exit survey that is not deleted," do
        let(:survey) {
          question = SingleSelectQuestion.create!(
            name: "office"
          )
          survey = Survey.create!(account: account, status: :active, type: :exit, name: "My Cool Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47), deleted_at: nil)
          survey.survey_to_questions.create!(
            question_id: question.id,
            status: :active
          )
          survey
        }
        context "when the report is not a hierarchy report," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "includes reports" do
              rag.touch
              expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}))
            end
          end
          context "when a response exists where the user is a reviewer," do
            let(:response) { Response.create!(reviewer: user, survey: survey) }

            it "includes reports" do
              response.touch
              expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}))
            end
          end
        end
      end
      context "when there is an exit survey that is deleted," do
        let(:survey) {
          question = SingleSelectQuestion.create!(
            name: "office"
          )
          survey = Survey.create!(account: account, status: :active, type: :exit, name: "My Cool Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47), deleted_at: DateTime.new(2018, 1, 3, 16, 47))
          survey.survey_to_questions.create!(
            question_id: question.id,
            status: :active
          )
          survey
        }
        context "when the report is not a hierarchy report," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "doesn't include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
          context "when a response exists where the user is a reviewer," do
            let(:response) { Response.create!(reviewer: user, survey: survey) }

            it "doesn't include reports" do
              response.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
        end
      end
      context "when there is an three_sixty survey that is not deleted," do
        let(:survey) {
          question = SingleSelectQuestion.create!(
            name: "office"
          )
          survey = Survey.create!(account: account, status: :active, type: :three_sixty, name: "My Cool Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47), deleted_at: nil)
          survey.survey_to_questions.create!(
            question_id: question.id,
            status: :active
          )
          survey
        }
        context "when the report is not a hierarchy report," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "includes reports" do
              rag.touch
              expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}))
            end
          end
          context "when a survey_topic exists where the current user is the subject," do
            let(:topic) { SurveyTopic.create!(subject: user, survey: survey, status: "shared") }

            it "includes reports with subject access" do
              topic.touch
              expect(reports[:reports]).to include(hash_including(surveyName: {en: survey.name}, access: "subject", subjectName: user.name))
            end
          end
        end
      end
      context "when the survey is deleted," do
        let(:survey) {
          question = SingleSelectQuestion.create!(
            name: "office"
          )
          survey = Survey.create!(account: account, status: :active, type: :engagement, name: "My Cool Survey", closed_at: DateTime.new(2018, 1, 3, 16, 47), deleted_at: DateTime.new(2018, 1, 3, 16, 47))
          survey.survey_to_questions.create!(
            question_id: question.id,
            status: :active
          )
          survey
        }
        context "when the report is not a hierarchy report," do
          let(:report) { Report.create!(survey: survey, name: "Report", sharing_status: "published", is_hierarchy_report: false) }
          context "when a report grant exists for the current user," do
            let(:rag) { ReportAccessGrant.create!(report_consumer: user, report: report, action_framework_role: ReportAccessGrant::VIEWER) }

            it "doesn't include reports" do
              rag.touch
              expect(reports[:reports]).not_to include(hash_including(surveyName: {en: survey.name}))
            end
          end
        end
      end
    end
  end
end
