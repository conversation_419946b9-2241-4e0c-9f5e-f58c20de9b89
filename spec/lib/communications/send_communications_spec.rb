require "rails_helper"
require_relative "../../../lib/communications/send_communications"

module Communications
  RSpec.describe SendCommunications do
    let(:slack_communication_variables) { double("slack comms variables") }
    let(:ms_teams_communication_variables) { double("ms teams comms variables") }
    let(:send_email) { double("send email", call: true) }
    let(:account) { double("account", subdomain: "camp", aggregate_id: "account-agg-id") }
    let(:response) { double("response") }
    let(:recipient) { double("recipient", aggregate_id: "recipient-agg-id") }
    let(:conversation) { double("conversation") }
    let(:survey) {
      double(
        "survey",
        account: account,
        aggregate_id: "survey-agg-id",
        type: :onboard,
        ms_teams_for_lifecycle_enabled?: false,
        ms_teams_for_effectiveness_enabled?: false
      )
    }
    let(:correlation_id) { SecureRandom.uuid }

    context "when the communcation is mailable" do
      it "sends comm via email" do
        emailable_config = double(
          "emailable comm configuration",
          send_by_email?: true,
          send_by_slack?: false,
          default_locale: "en",
          template_type: "invite_user",
          additional_fields: {},
          survey: survey,
          recipient: recipient,
          response: response,
          correlation_id: correlation_id,
          conversation: conversation
        )

        SendCommunications.new(
          slack_communication_variables: slack_communication_variables,
          ms_teams_communication_variables: ms_teams_communication_variables,
          send_email: send_email
        ).call(
          configurations: [emailable_config]
        )

        expect(send_email).to have_received(:call)
          .with(
            account: account,
            survey: survey,
            response: response,
            recipient: recipient,
            template_type: "invite_user",
            locale: "en",
            additional_fields: {},
            conversation: conversation,
            correlation_id: correlation_id
          )
      end

      context "when the survey also has slack enabled" do
        let(:slack_direct_message) { double("slack_direct_message", call: true) }
        let(:slack_communication_content) { double("slack_communication_content") }
        let(:survey) {
          double(
            "survey",
            account: account,
            aggregate_id: "survey-agg-id",
            id: "survey-bson-id",
            type: :onboard,
            ms_teams_for_lifecycle_enabled?: false,
            ms_teams_for_effectiveness_enabled?: false
          )
        }

        it "sends the comm via slack" do
          emailable_config = double(
            "emailable comm configuration",
            send_by_email?: true,
            send_by_slack?: true,
            default_locale: "en",
            template_type: "invite_user",
            additional_fields: {},
            survey: survey,
            recipient: recipient,
            response: response,
            conversation: conversation,
            correlation_id: correlation_id
          )

          slack_variables_container = double
          slack_msg_content = double
          allow(slack_communication_variables).to receive(:call)
            .with(
              survey: survey,
              response: response,
              locale: "en",
              additional_variables: {}
            ).and_return(slack_variables_container)
          allow(slack_communication_content).to receive(:call)
            .with(
              survey_id: "survey-bson-id",
              survey_type: :onboard,
              template_type: "invite_user",
              locale: "en"
            ).and_return(slack_msg_content)
          allow(slack_variables_container).to receive(:call)
            .with(content: slack_msg_content)
            .and_return("slack msg with replaced variables")

          SendCommunications.new(
            slack_communication_variables: slack_communication_variables,
            slack_direct_message: slack_direct_message,
            slack_communication_content: slack_communication_content,
            ms_teams_communication_variables: ms_teams_communication_variables,
            send_email: send_email
          ).call(
            configurations: [emailable_config]
          )

          expect(slack_direct_message).to have_received(:call)
            .with(
              account: account,
              employee: recipient,
              text: "slack msg with replaced variables"
            )
        end
      end

      context "when the survey also has ms teams enabled" do
        let(:ms_teams_direct_message) { double("send ms teams dm", call: true) }

        [
          {ms_teams_for_lifecycle_enabled: true, ms_teams_for_effectiveness_enabled: false},
          {ms_teams_for_lifecycle_enabled: false, ms_teams_for_effectiveness_enabled: true},
          {ms_teams_for_lifecycle_enabled: true, ms_teams_for_effectiveness_enabled: true} # not possible logically because survey can of either type
        ].each do |survey_configuration|
          it " with survey configuration #{survey_configuration}, sends comm via ms teams" do
            emailable_config = double(
              "emailable comm configuration",
              send_by_email?: true,
              send_by_slack?: false,
              default_locale: "en",
              template_type: "invite_user",
              additional_fields: {},
              survey: survey,
              recipient: recipient,
              response: response,
              conversation: conversation,
              correlation_id: correlation_id
            )
            allow(survey).to receive(:ms_teams_for_lifecycle_enabled?)
              .and_return(survey_configuration[:ms_teams_for_lifecycle_enabled])
            allow(survey).to receive(:ms_teams_for_effectiveness_enabled?)
              .and_return(survey_configuration[:ms_teams_for_effectiveness_enabled])

            allow(ms_teams_communication_variables).to receive(:to_hash)
              .with(
                survey: survey,
                response: response,
                locale: "en"
              ).and_return({})
            allow(I18n).to receive(:t)
              .with("msteams.onboard.invite_user", locale: "en")
              .and_return("ms teams msg with replaced variables")

            SendCommunications.new(
              slack_communication_variables: slack_communication_variables,
              ms_teams_communication_variables: ms_teams_communication_variables,
              ms_teams_direct_message: ms_teams_direct_message,
              send_email: send_email
            ).call(
              configurations: [emailable_config]
            )

            expect(ms_teams_direct_message).to have_received(:call)
              .with(
                account: account,
                person: recipient,
                message: "ms teams msg with replaced variables"
              )
          end
        end
      end
    end

    context "when communication is not mailable" do
      it "does not send comm via email" do
        non_emailable_config = double(
          "non emailable comm configuration",
          send_by_email?: false,
          send_by_slack?: false
        )

        SendCommunications.new(
          slack_communication_variables: slack_communication_variables,
          ms_teams_communication_variables: ms_teams_communication_variables,
          send_email: send_email
        ).call(
          configurations: [non_emailable_config]
        )

        expect(send_email).to_not have_received(:call)
      end

      it "also does not send comm via other channels, even if they are enabled" do
        slack_direct_message = double("slack_direct_message", call: true)
        ms_teams_direct_message = double("send ms teams dm", call: true)

        non_emailable_config = double(
          "non emailable comm configuration",
          send_by_email?: false,
          send_by_slack?: true
        )

        allow(survey).to receive(:ms_teams_for_lifecycle_enabled?)
          .and_return(true)
        allow(survey).to receive(:ms_teams_for_effectiveness_enabled?)
          .and_return(true)

        SendCommunications.new(
          slack_communication_variables: slack_communication_variables,
          ms_teams_communication_variables: ms_teams_communication_variables,
          slack_direct_message: slack_direct_message,
          ms_teams_direct_message: ms_teams_direct_message,
          send_email: send_email
        ).call(
          configurations: [non_emailable_config]
        )

        expect(slack_direct_message).to_not have_received(:call)
        expect(ms_teams_direct_message).to_not have_received(:call)
      end
    end
  end
end
