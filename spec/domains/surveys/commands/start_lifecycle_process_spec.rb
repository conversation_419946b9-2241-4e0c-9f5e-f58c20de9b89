require "rails_helper"

RSpec.describe Surveys::Commands::StartLifecycleProcess do
  let(:lifecycle_communications) { instance_double(Lifecycle::LifecycleCommunications, process_created: true) }
  let(:employee) { instance_double(Person, id: BSON::ObjectId.new, name_sort: "name_sort") }
  let(:interviewer) { instance_double(Person, id: BSON::ObjectId.new, invited?: true) }
  let(:survey) { instance_double(Survey, id: BSON::ObjectId.new, full_workflow?: true) }
  let(:create_response) { instance_double(Responses::Commands::CreateResponse) }
  let(:survey_topic) do
    instance_spy(
      SurveyTopic,
      survey: survey,
      subject: employee,
      interviewer: interviewer,
      send_survey_to: send_survey_to,
      to_h: {
        interviewer_id: interviewer.try(:id),
        subject_id: employee.id,
        survey_id: survey.id
      }.with_indifferent_access
    )
  end
  let(:response) do
    instance_spy(
      Response,
      survey: survey,
      user: employee,
      reviewer: interviewer,
      type: :subject
    ).tap do |it|
      allow(it).to receive(:publish)
    end
  end
  let(:send_survey_to) { :employee }

  let(:correlation_id) { SecureRandom.uuid }

  let(:start_lifecycle_params) do
    {
      survey_topic: survey_topic,
      correlation_id: correlation_id
    }
  end

  let(:start_lifecycle_process) do
    described_class.new(
      create_response: create_response,
      lifecycle_communications: lifecycle_communications
    )
  end

  before do
    allow(create_response).to receive(:call).and_return(response)
    allow(survey_topic).to receive(:save!)
  end

  it "creates a response" do
    expect(create_response).to receive(:call).with(
      user_id: employee.id,
      survey: survey,
      survey_topic: survey_topic
    )

    expect(survey_topic).to receive(:responses=).with([response])
    expect(survey_topic).to receive(:save!)
    start_lifecycle_process.call(**start_lifecycle_params)
  end

  it "adds a sorting tag to the survey topic" do
    expect(survey_topic).to receive(:sort_tag=).with("name_sort")
    start_lifecycle_process.call(**start_lifecycle_params)
  end

  it "ensures the topic is on the issued state" do
    expect(survey_topic).to receive(:issue)
    expect(survey_topic).to receive(:save!)
    start_lifecycle_process.call(**start_lifecycle_params)
  end

  it "trigger notifications" do
    expect(lifecycle_communications).to receive(:process_created).with(survey_topic, correlation_id)
    start_lifecycle_process.call(**start_lifecycle_params)
  end

  context "interviewer is a new user" do
    before { allow(interviewer).to receive(:invited?).and_return(false) }

    it "notifies the interviewer they have a new account" do
      expect(interviewer).to receive(:notify_new_account)
      start_lifecycle_process.call(**start_lifecycle_params)
    end
  end

  context "no interviewer (edge case: basic workflow process created on survey later changed to full workflow)" do
    let(:interviewer) { nil }

    it "does not crash" do
      start_lifecycle_process.call(**start_lifecycle_params)
    end
  end

  context "skip employee response" do
    let(:send_survey_to) { :no_one }

    it "creates a submitted response" do
      expect(response).to receive(:update!).with(hash_including(status: :submitted))
      expect(survey_topic).to receive(:update!).with(hash_including(status: :submitted))
      start_lifecycle_process.call(**start_lifecycle_params)
    end
  end
end
