require "rails_helper"
require "spec_access_helper"

RSpec.describe NewComparisonReportController do
  let(:account) { FactoryBot.create(:account_with_master) }
  let(:survey) do
    FactoryBot.create(:survey, account: account)
  end
  let(:superuser) { SpecAccessHelper.construct_superuser_for_privileged_account(account) }

  before do
    sign_in superuser
  end

  describe "#show" do
    describe "default behaviour" do
      before do
        get :show, params: {survey_id: survey.id, report_id: "admin"}
      end

      it_behaves_like "an ok response"
    end

    describe "with multi-demographic anchors" do
      let(:department) { FactoryBot.create(:segment_question, name: "Department", account: account) }
      let(:location) { FactoryBot.create(:segment_question, name: "Location", account: account) }
      let(:department_stq) { survey.survey_to_questions.create(question: department, type: department.type) }
      let(:location_stq) { survey.survey_to_questions.create(question: location, type: location.type) }
      let(:department_option) { department.select_options.first }
      let(:location_option) { location.select_options.first }

      let(:results) { JSON.parse(response.body) }

      before do
        allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
          .to receive(:has_access_to_view_multi_demographic_reports?)
          .and_return(true)

        get :show, params: {
          survey_id: survey.id,
          report_id: "admin",
          a: "#{department_stq.id}-#{department_option.id},#{location_stq.id}-#{location_option.id}"
        }
      end

      it_behaves_like "an ok response"
    end
  end

  describe "#count" do
    let(:demographic) { FactoryBot.create(:segment_question) }
    let(:demographic_stq) { FactoryBot.build(:survey_to_question, question: demographic) }
    let(:option) { demographic.select_options.last }
    let(:count) { 2 }
    let(:overall) { 20 }

    let(:results) { JSON.parse(response.body) }

    let(:sri_service) do
      double(
        SnapshotReportingEngineService,
        overall_participation_result: participation_result,
        filter_significance_result: filter_significance_result
      )
    end
    let(:error) { false }
    let(:filtered_population) { 20 }
    let(:overall_population) { 100 }
    let(:participation_data) do
      Reporting::Data::Participation::Result.with(
        filtered_result: double(submission_count: filtered_population),
        overall_result: double(submission_count: overall_population),
        segment_class_results: [],
        hierarchy_spreads: [],
        cross_spreads: []
      )
    end
    let(:participation_result) { double(Monad::Result, error?: error, data: participation_data) }
    let(:contains_insignificant_filters) { false }
    let(:filter_significance_result) { instance_double(Reporting::Data::FilterSignificanceCheck, contains_insignificant_filters?: contains_insignificant_filters) }

    before do
      allow(SnapshotReportingEngineService).to receive(:new).and_return(sri_service)
      get :count, params: {survey_id: survey.id, report_id: "admin"}
    end

    context "query is down" do
      let(:error) { true }

      it "returns a count of -1" do
        expect(results["count"]).to eq(-1)
      end

      it "returns a total population of -1" do
        expect(results["total_responses"]).to eq(-1)
      end

      it "returns the significant population" do
        expect(results["significant_population"]).to eq 5
      end
    end

    context "count is not significant" do
      let(:filtered_population) { 2 }

      it_behaves_like "an ok response"

      it "includes the significant population" do
        expect(results["significant_population"]).to eq 5
      end

      it "has a -1 for count" do
        expect(results["count"]).to eq(-1)
      end

      it "includes the total number of responses" do
        expect(results["total_responses"]).to eq(overall_population)
      end
    end

    context "count is significant" do
      let(:count) { 12 }

      it_behaves_like "an ok response"

      it "includes the significant population" do
        expect(results["significant_population"]).to eq 5
      end

      it "has the count" do
        expect(results["count"]).to eq(filtered_population)
      end

      it "includes the total number of responses" do
        expect(results["total_responses"]).to eq(overall_population)
      end

      context "contains insignificant filters" do
        let(:contains_insignificant_filters) { true }

        it "includes the significant population" do
          expect(results["significant_population"]).to eq 5
        end

        it "count is -1" do
          expect(results["count"]).to eq(-1)
        end

        it "includes the total number of responses" do
          expect(results["total_responses"]).to eq(overall_population)
        end
      end
    end
  end
end
