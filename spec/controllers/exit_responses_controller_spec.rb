require "rails_helper"
require "spec_access_helper"

RSpec.describe ExitResponsesController do
  let(:account) { FactoryBot.create(:account_without_users, name: "Exit Controller", subdomain: "exitcontroller") }
  let(:subject_user) { FactoryBot.create(:user, account: account) }
  let(:reviewer) { FactoryBot.create(:admin, account: account) }
  let(:user) { reviewer }
  let(:response_type) { nil }
  let(:response_status) { :issued }
  let(:survey_topic_id) { "1" }
  let(:survey_type) { :exit }

  let(:survey) do
    Survey.create!(
      account: account,
      name: "survey",
      type: survey_type
    )
  end

  let(:survey_topic) do
    SurveyTopic.new(
      id: survey_topic_id,
      survey: survey,
      subject: subject_user,
      interviewer: reviewer,
      status: response_status,
      survey_due_date: Date.today + 2.weeks,
      interview_due_date: Date.today + 2.weeks
    )
  end

  before do
    acc = Account.subdomain("exitcontroller")
    acc&.delete
    resign_in user
  end

  describe "#demo_mode" do
    before do
      allow(SurveyTopic).to receive(:find).with(survey_topic_id).and_return(survey_topic)
      allow(UserFocus).to receive(:save_focus)
    end

    context "not set for survey" do
      it "is forbidden" do
        get :demo_mode, params: {id: survey_topic_id}
        expect(response.status).to eq 403
      end
    end

    context "demo mode set" do
      let(:subject_response) { instance_double(Response) }
      before do
        survey.assign_config!("demo_mode", true)
      end

      it "renders the demo mode" do
        get :demo_mode, params: {id: survey_topic_id}
        expect(response).to render_template("exit_responses/demo_mode")
      end

      it "redirects update to demo" do
        allow(survey_topic).to receive(:persisted?) { true }
        allow(survey_topic).to receive(:subject_response) { subject_response }
        allow(subject_response).to receive(:save!)
        put :update, params: {id: survey_topic_id, field_name: "classify", question_ids: ""}
        expect(response).to redirect_to(demo_exit_responses_path(survey_topic.id))
      end
    end
  end

  describe "#create" do
    let(:employee_response) { instance_double(Response) }
    let(:new_survey_topic) do
      SurveyTopic.new(survey: survey)
    end

    let(:params) do
      {
        survey_id: survey.id,
        employee_id: subject_user.aggregate_id,
        reviewer_id: reviewer.aggregate_id,
        survey_due: "2015-11-01",
        interview_due: "2015-11-20",
        last_day_of_employment: (Time.now + 2.weeks).strftime("%Y-%m-%d")
      }
    end

    let(:start_lifecycle_process) { instance_double(Surveys::Commands::StartLifecycleProcess) }

    before do
      allow(new_survey_topic).to receive(:save!)
      allow(new_survey_topic).to receive(:subject_response).and_return(employee_response)
      allow(SurveyTopic).to receive(:new).with(survey: survey).and_return(new_survey_topic)
      allow(Surveys::Commands::StartLifecycleProcess).to receive(:new).and_return(start_lifecycle_process)
      allow(start_lifecycle_process).to receive(:call)
    end

    it "starts the process" do
      expect(start_lifecycle_process).to receive(:call).with(survey_topic: new_survey_topic, correlation_id: anything)
      post :create, params: params
    end

    it "sets the SurveyTopic data fields" do
      post :create, params: params
      expect(new_survey_topic.subject).to eq(subject_user)
    end

    it "redirects to index" do
      expect(start_lifecycle_process).to receive(:call)
      post :create, params: params
      expect(response).to redirect_to survey_exit_responses_path(survey)
    end

    it "does not create survey topic if it already exists for the employee" do
      allow(SurveyTopic).to receive(:new).and_call_original
      SurveyTopicFactory.new(survey, user: subject_user, reviewer: reviewer).build
      expect(start_lifecycle_process).not_to receive(:call)
      post :create, params: params
      expect(response).to redirect_to survey_exit_responses_path(survey)
    end

    context "when there are redirect parameters" do
      context "with given path" do
        let(:params_with_redirect) do
          params.merge(redirect: "/surveys")
        end

        it "redirects to given path" do
          post :create, params: params_with_redirect
          expect(response).to redirect_to("/surveys")
        end
      end

      context "with outside url" do
        let(:params_with_redirect) do
          params.merge(redirect: "http://www.malicious.com")
        end

        it "does not redirect outside Culture Amp" do
          post :create, params: params_with_redirect
          expect(response).to_not redirect_to("http://www.malicious.com")
        end
      end
    end

    context "on event sourced account" do
      let(:account) { FactoryBot.create(:account_without_users, import_mode: "influx") }
      let(:employee_params) do
        {
          name: subject_user.name,
          preferred_name: subject_user.preferred_name,
          employee_id: subject_user.employee_id,
          email: subject_user.email,
          date_of_birth: subject_user.date_of_birth&.iso8601,
          start_date: subject_user.start_date&.iso8601,
          end_date: (Date.today + 2.weeks).iso8601,
          locale: subject_user.locale,
          observer: false
        }
      end
      let(:employee_service) { instance_double(Services::EmployeeService) }

      before { allow(Services::EmployeeService).to receive(:new).and_return(employee_service) }

      it "updates the end date via employee service" do
        expect(employee_service).to receive(:update)
          .with(subject_user.aggregate_id, employee_params, account.aggregate_id, user.aggregate_id)
          .and_return([])

        post :create, params: params
      end

      context "when errors returned during employee deactivation" do
        it "raises an error" do
          expect(employee_service).to receive(:update)
            .with(subject_user.aggregate_id, employee_params, account.aggregate_id, user.aggregate_id)
            .and_return(["some error"])

          expect { post :create, params: params }.to raise_error(RuntimeError)
        end
      end
    end
  end

  describe "#update" do
    before do
      allow(SurveyTopic).to receive(:find).with(survey_topic_id).and_return(survey_topic)
      allow(survey_topic).to receive(:persisted?) { true }
      allow(survey_topic).to receive(:save!)
    end

    describe "field_name: classify" do
      let(:subject_response) { instance_double(Response) }

      before do
        allow(survey_topic).to receive(:subject_response) { subject_response }
      end

      it "saves response and redirects to the response path" do
        expect(subject_response).to receive(:save!)
        put :update, params: {id: survey_topic_id, field_name: "classify", question_ids: ""}
        expect(response).to redirect_to(survey_exit_responses_path(survey))
      end
    end

    describe "field_name: interview-close" do
      before { allow(survey_topic).to receive(:complete_interview!) }

      it "moves the survey topic to the interviewed state" do
        expect(survey_topic).to receive(:complete_interview!)
        put :update, params: {id: survey_topic_id, field_name: "interview-close"}
      end

      it "does not send emails" do
        put :update, params: {id: survey_topic_id, field_name: "interview-close"}
        expect(ActionMailer::Base.deliveries).to be_empty
      end

      it "redirects to the response path" do
        put :update, params: {id: survey_topic_id, field_name: "interview-close"}
        expect(response).to redirect_to(exit_response_path(survey_topic.id))
      end
    end

    describe "field_name: interview-reopen" do
      it "moves the survey topic back to issued" do
        expect(survey_topic).to receive(:reopen_interview!)
        put :update, params: {id: survey_topic_id, field_name: "interview-reopen"}
      end
    end

    describe "field-name: skip" do
      before { allow(survey_topic).to receive(:skip_subject!) }

      it "skips the subject response" do
        expect(survey_topic).to receive(:skip_subject!).with(hash_including(as_user: user))
        put :update, params: {id: survey_topic_id, field_name: "skip"}
      end

      it "redirects to the response path" do
        put :update, params: {id: survey_topic_id, field_name: "skip"}
        expect(response).to redirect_to(exit_response_path(survey_topic.id))
      end
    end

    describe "field-name: close" do
      before do
        allow(survey_topic).to receive(:close!)
      end

      it "closes the survey topic" do
        expect(survey_topic).to receive(:close!)
        put :update, params: {id: survey_topic_id, field_name: "close"}
      end

      it "redirects to the index" do
        put :update, params: {id: survey_topic_id, field_name: "close"}
        expect(response).to redirect_to(survey_exit_responses_path(survey))
      end
    end

    describe "field-name: reopen" do
      context "not admin" do
        before do
          resign_in subject_user
        end

        it "renders forbidden" do
          put :update, params: {id: survey_topic_id, field_name: "reopen"}
          expect(response.code).to eq("403")
          expect(response).to render_template("site/403.html.haml")
        end
      end

      context "authorised" do
        it "reopens the survey topic" do
          expect(survey_topic).to receive(:reopen!)
          put :update, params: {id: survey_topic_id, field_name: "reopen"}
        end

        it "redirects to the response path" do
          expect(survey_topic).to receive(:reopen!)
          put :update, params: {id: survey_topic_id, field_name: "reopen"}
          expect(response).to redirect_to(exit_response_path(survey_topic.id))
        end
      end
    end

    describe "field-name: manager" do
      let!(:survey_topic) do
        SurveyTopic.delete_all
        SurveyTopic.create(
          survey: survey,
          subject: subject_user,
          interviewer: reviewer,
          status: response_status,
          survey_due_date: Date.today + 2.weeks,
          interview_due_date: Date.today + 2.weeks
        )
      end

      let(:manager) { FactoryBot.create(:user, account: account) }

      before do
        allow(SurveyTopic).to receive(:find).and_call_original
      end

      context "basic workflow" do
        before do
          survey.assign_config!(Configs::LIFECYCLE_WORKFLOW, Configs::LIFECYCLE_WORKFLOW_BASIC)
        end

        it "does not change the interviewer" do
          put :update, params: {id: survey_topic.id, field_name: "manager", manager_id: manager.aggregate_id}
          expect(survey_topic.interviewer).to eq reviewer
        end

        it "redirects to the index page" do
          put :update, params: {id: survey_topic.id, field_name: "manager", manager_id: manager.aggregate_id}
          expect(response).to redirect_to(survey_exit_responses_path(survey))
        end
      end

      context "full workflow" do
        let(:lifecycle_communications) { instance_double(Lifecycle::LifecycleCommunications, reviewer_updated: true) }

        before do
          FactoryBot.create(
            :subject_response,
            :attach_survey_topic,
            survey: survey,
            survey_topic: survey_topic,
            reviewer: reviewer,
            user: user
          )
        end

        before do
          allow(Lifecycle::LifecycleCommunications).to receive(:new) { lifecycle_communications }
        end

        it "sets the new interviewer" do
          put :update, params: {id: survey_topic.id, field_name: "manager", manager_id: manager.aggregate_id}
          survey_topic.reload
          expect(survey_topic.interviewer).to eq manager
        end

        it "redirects to the index page" do
          put :update, params: {id: survey_topic.id, field_name: "manager", manager_id: manager.aggregate_id}
          expect(response).to redirect_to(survey_exit_responses_path(survey))
        end

        it "trigger the process updated communications" do
          expect(lifecycle_communications).to receive(:reviewer_updated)
          put :update, params: {id: survey_topic.id, field_name: "manager", manager_id: manager.aggregate_id}
        end
      end
    end
  end

  describe "#index" do
    it "renders the lifecycle index" do
      get :index, params: {survey_id: survey.id}
      expect(response).to render_template("exit_responses/index")
    end

    context "for an async request" do
      it "renders just the response list" do
        get :index, params: {survey_id: survey.id}, xhr: true
        expect(response).to render_template("exit_responses/_response_list")
      end
    end

    context "for a 360 survey" do
      let(:survey) { instance_double(Survey, id: 1, three_sixty?: true, account: account) }
      let(:three_sixty_paths) { instance_double(Navigation::ThreeSixtyPaths, index: "360_index") }

      before do
        allow(Navigation::ThreeSixtyPaths).to receive(:new).with(survey, user) { three_sixty_paths }
        allow(Survey).to receive(:find) { survey }
        allow(UserFocus).to receive(:save_focus)
      end

      it "redirects to 360 controller" do
        get :index, params: {survey_id: survey.id}
        expect(response).to redirect_to("360_index")
      end
    end

    context "as reviewer" do
      let!(:response) do
        Response.create!(
          user: subject_user,
          reviewer: reviewer,
          survey: survey,
          survey_topic: survey_topic
        )
      end

      let(:survey_topic) do
        SurveyTopic.create(
          survey: survey,
          subject: subject_user,
          interviewer: reviewer,
          status: response_status,
          survey_due_date: Date.today + 2.weeks,
          interview_due_date: Date.today + 2.weeks
        )
      end

      before :each do
        resign_in reviewer
        get :index, params: {survey_id: survey.id}
      end

      it "allows reviewers to view their exit responses" do
        expect(controller.survey).to eq(survey)
        expect(controller.responses).to include(survey_topic)
      end
    end

    context "as subject" do
      let!(:response) do
        Response.create!(
          user: subject_user,
          reviewer: reviewer,
          survey: survey,
          survey_topic: survey_topic
        )
      end

      let(:survey_topic) do
        SurveyTopic.create(
          survey: survey,
          subject: subject_user,
          interviewer: reviewer,
          status: response_status,
          survey_due_date: Date.today + 2.weeks,
          interview_due_date: Date.today + 2.weeks
        )
      end

      let(:survey_type) { :exit }

      before :each do
        resign_in subject_user
        get :index, params: {survey_id: survey.id}
      end

      it "does not allow subjects to view their exit responses" do
        expect(controller.responses).not_to include(survey_topic)
      end
    end
  end

  describe "#user_search" do
    let!(:adhoc_user) { FactoryBot.create(:user, account: account, association_type: :adhoc) }
    let(:superuser) { SpecAccessHelper.construct_superuser_for_privileged_account(survey.account) }

    before do
      resign_in authorised_user
      get :user_search,
        params: {
          term: subject_user.name,
          survey_id: survey.id,
          type: "employee-step"
        },
        xhr: true
    end

    context "user is lifecycle admin" do
      let(:authorised_user) { reviewer }

      it "allows the user to search" do
        expect(response).to render_template("exit_responses/user_search")
      end

      it "does not show adhoc users" do
        expect(controller.people.include?(adhoc_user)).not_to eq(true)
      end
    end

    context "user is not admin" do
      let(:authorised_user) { subject_user }

      it "is forbidden" do
        expect(response.code).to eq "403"
      end
    end

    context "with super user" do
      let(:authorised_user) { superuser }

      it "allows the user to search" do
        expect(response).to render_template("exit_responses/user_search")
      end
    end
  end

  describe "#destroy" do
    before do
      allow(SurveyTopic).to receive(:find).with(survey_topic._id).and_return(survey_topic)
    end

    it "deletes the survey topic" do
      expect(survey_topic).to receive(:delete!)
      delete :destroy, params: {id: survey_topic_id}
    end

    it "redirects to the response path" do
      delete :destroy, params: {id: survey_topic_id}
      expect(response).to redirect_to(survey_exit_responses_path(survey))
    end
  end

  describe "#show" do
    let(:account) { Account.new }
    let(:survey) { Survey.new(account: account) }

    before do
      allow(SurveyTopic).to receive(:find).with(survey_topic._id).and_return(survey_topic)
    end

    context "valid survey topic and user" do
      let(:account) { FactoryBot.create(:account) }
      # Some URLs generators need survey to be persistent
      let(:survey) { FactoryBot.create(:basic_survey, account: account) }

      before do
        allow(survey_topic).to receive(:subject_response).and_return(Response.new)
        allow(Lifecycle::ProcessActions).to receive(:new) { double(for: [:view]) }
      end

      it "gets the employee" do
        get :show, params: {id: survey_topic_id}
        expect(assigns[:employee]).to eq subject_user
      end

      it "gets the reviewer" do
        get :show, params: {id: survey_topic_id}
        expect(assigns[:reviewer]).to eq reviewer
      end

      it "is OK" do
        get :show, params: {id: survey_topic_id}
        expect(response).to be_ok
      end

      context "exit/onboard full workflow" do
        before do
          allow(survey).to receive(:full_workflow?) { true }
          allow(survey).to receive(:lifecycle_close_step?) { true }
        end

        it "shows the right sections" do
          get :show, params: {id: survey_topic_id}
          expect(controller.valid_sections).to eq [:response, :interview, :classify]
        end
      end

      context "exit/onboard basic workflow" do
        before do
          allow(survey).to receive(:full_workflow?) { false }
          allow(survey).to receive(:lifecycle_close_step?) { true }
        end

        it "shows the right sections" do
          get :show, params: {id: survey_topic_id}
          expect(controller.valid_sections).to eq [:response, :classify]
        end
      end

      context "exit/onboard with no classification questions" do
        before do
          allow(survey).to receive(:full_workflow?) { true }
          allow(survey).to receive(:lifecycle_close_step?) { false }
        end

        it "shows the right sections" do
          get :show, params: {id: survey_topic_id}
          expect(controller.valid_sections).to eq [:response, :interview]
        end
      end

      context "for a 360 survey" do
        let(:three_sixty_paths) { instance_double(Navigation::ThreeSixtyPaths, get: "360_get") }

        before do
          allow(survey).to receive(:three_sixty?) { true }
          allow(Navigation::ThreeSixtyPaths).to receive(:new).with(survey, user) { three_sixty_paths }
        end

        it "redirects to 360 controller" do
          get :show, params: {id: survey_topic_id}
          expect(response).to redirect_to("360_get")
        end
      end
    end

    context "not allowed user" do
      before do
        allow(Lifecycle::ProcessActions).to receive(:new) { double(for: []) }
        allow(UserFocus).to receive(:save_focus)
      end

      it "returns forbidden" do
        get :show, params: {id: survey_topic_id}
        expect(response).to be_forbidden
      end
    end

    context "survey topic is deleted" do
      before do
        allow(survey_topic).to receive(:deleted?) { true }
        allow(UserFocus).to receive(:save_focus)
      end

      it "renders the deleted template" do
        get :show, params: {id: survey_topic_id}
        expect(response).to have_rendered(:deleted)
      end
    end

    context "survey is deleted" do
      before do
        allow(survey).to receive(:deleted?) { true }
        allow(UserFocus).to receive(:save_focus)
      end

      it "returs not found" do
        get :show, params: {id: survey_topic_id}
        expect(response).to be_not_found
      end
    end
  end

  describe "#count" do
    render_views

    before do
      SurveyTopic.delete_all
      topic1 = SurveyTopic.create(
        survey: survey,
        subject: subject_user,
        interviewer: reviewer,
        status: :issued,
        survey_due_date: Date.today + 2.weeks
      )
      Response.create(
        survey: survey,
        survey_topic: topic1,
        user: subject_user,
        reviewer: reviewer
      )
      topic2 = SurveyTopic.create(
        survey: survey,
        subject: subject_user,
        interviewer: reviewer,
        status: :completed,
        survey_due_date: Date.today + 2.weeks
      )
      Response.create(
        survey: survey,
        survey_topic: topic2,
        user: subject_user,
        reviewer: reviewer
      )
      get :count, params: params
    end

    let(:json_body) { JSON.parse(response.body) }

    context "with no filters" do
      let(:params) { {format: "json", survey_id: survey.id} }

      it "returns the count of matching responses" do
        expect(json_body["count"]).to eq 2
      end

      it "returns the count of total responses" do
        expect(json_body["total_responses"]).to eq 2
      end

      it "includes the signficant population" do
        expect(json_body["significant_population"]).to eq 0
      end
    end

    context "with filters" do
      let(:params) { {format: "json", survey_id: survey.id, status: "issued"} }

      it "returns the count of matching responses" do
        expect(json_body["count"]).to eq 1
      end

      it "returns the count of total responses" do
        expect(json_body["total_responses"]).to eq 2
      end

      it "includes the signficant population" do
        expect(json_body["significant_population"]).to eq 0
      end
    end
  end
end
