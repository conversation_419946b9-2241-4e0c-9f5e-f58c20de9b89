require "rails_helper"

RSpec.describe Lifecycle::LifecycleCommunications do
  let(:account) { double }
  let(:send_communications) do
    Class.new {
      def call(configurations:)
        @delivered = configurations
      end

      def delivered
        @delivered || []
      end
    }.new
  end
  let(:user) { instance_double(Person, "user", select_preferred_locale: "en") }
  let(:survey) { instance_double(Survey, "survey", supported_locales: [], account: account) }
  let(:reviewer) { instance_double(Person, "reviewer", select_preferred_locale: "en") }
  let(:user_response) do
    instance_double(Response, user: user, survey: survey, reviewer: reviewer, incomplete?: false, send_survey_to: :employee)
  end
  let(:user_conversation) { instance_double(Conversation) }
  let(:reviewer_conversation) { instance_double(Conversation) }
  let(:process) { instance_double(SurveyTopic, subject_response: user_response, reviewer_responses: [], send_survey_to: :employee, survey: survey) }
  let(:correlation_id) { SecureRandom.uuid }

  subject(:notifications) do
    Lifecycle::LifecycleCommunications.new(
      send_communications: send_communications
    )
  end

  before do
    allow(account).to receive(:churned?) { false }
    allow(user_response).to receive(:conversation).with(:participant) { user_conversation }
    allow(user_response).to receive(:conversation).with(:reviewer) { reviewer_conversation }
    allow(user_response).to receive(:update_attributes)
    allow(user_response).to receive(:reminder_status)
  end

  describe "when feedback is collected" do
    context "process is not waiting for any responses" do
      before do
        allow(process).to receive(:awaiting_responses?) { false }
      end

      it "notifies the reviewer, if the reviewer is present" do
        notifications.feedback_collected(process)

        expect(send_communications.delivered).to include(
          Communications::CommunicationConfiguration.new(
            response: user_response,
            conversation: reviewer_conversation,
            recipient: reviewer,
            template_type: "survey_complete_reviewer"
          )
        )
      end

      it "does nothing if reviewer is not there" do
        allow(user_response).to receive(:reviewer) { nil }
        notifications.feedback_collected(process)
        expect(send_communications.delivered).to eq([])
      end
    end

    context "process is awaiting responses" do
      before do
        allow(process).to receive(:awaiting_responses?) { true }
      end

      it "does nothing" do
        notifications.feedback_collected(process)
        expect(send_communications.delivered).to eq([])
      end
    end
  end

  describe "collection reminders sent" do
    before do
      allow(process).to receive(:awaiting_responses?) { true }
    end

    context "process is set to send survey to user" do
      before do
        allow(process).to receive(:send_survey_to) { :employee }
      end

      it "notifies the user" do
        notifications.collection_reminders_sent(process)

        expect(send_communications.delivered).to include(
          Communications::CommunicationConfiguration.new(
            response: user_response,
            conversation: user_conversation,
            recipient: user,
            template_type: "survey_reminder"
          )
        )
      end
    end

    context "process is set to send survey to reviewer on the users behalf" do
      before do
        allow(process).to receive(:send_survey_to) { :reviewer }
      end

      it "notifies the reviewer" do
        notifications.collection_reminders_sent(process)

        expect(send_communications.delivered).to include(
          Communications::CommunicationConfiguration.new(
            response: user_response,
            conversation: reviewer_conversation,
            recipient: reviewer,
            template_type: "survey_reminder_on_employee_behalf"
          )
        )
      end
    end

    it "does nothing if the process has been submitted" do
      allow(process).to receive(:awaiting_responses?) { false }
      notifications.collection_reminders_sent(process)
      expect(send_communications.delivered).to eq([])
    end
  end

  describe "interview reminders sent" do
    before do
      allow(process).to receive(:awaiting_responses?) { false }
    end

    it "notifies the user if the process has a reviewer" do
      notifications.interview_reminders_sent(process)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: reviewer_conversation,
          recipient: reviewer,
          template_type: "interview_reminder"
        )
      )
    end

    it "does nothing if reviewer is not present" do
      allow(user_response).to receive(:reviewer) { nil }
      notifications.interview_reminders_sent(process)
      expect(send_communications.delivered).to eq([])
    end

    it "does nothing if process is still awaiting responses" do
      allow(process).to receive(:awaiting_responses?) { true }
      notifications.interview_reminders_sent(process)
      expect(send_communications.delivered).to eq([])
    end
  end

  describe "process is created" do
    it "invites the user" do
      notifications.process_created(process, correlation_id)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: user_conversation,
          recipient: user,
          correlation_id: correlation_id,
          template_type: "invite_user"
        )
      )
    end

    it "invites the reviewer if present" do
      notifications.process_created(process, correlation_id)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: reviewer_conversation,
          recipient: reviewer,
          correlation_id: correlation_id,
          template_type: "invite_reviewer"
        )
      )
    end

    context "if the process is configured to send survey to the reviewer" do
      it "invites the reviewer with a different template" do
        allow(process).to receive(:send_survey_to) { :reviewer }
        notifications.process_created(process, correlation_id)

        expect(send_communications.delivered).to include(
          Communications::CommunicationConfiguration.new(
            response: user_response,
            conversation: reviewer_conversation,
            recipient: reviewer,
            correlation_id: correlation_id,
            template_type: "invite_reviewer_on_employee_behalf"
          )
        )
      end
    end

    context "if the process is configured to send the survey to no one" do
      it "invites the reviewer with a different template" do
        allow(process).to receive(:send_survey_to) { :no_one }
        notifications.process_created(process, correlation_id)

        expect(send_communications.delivered).to include(
          Communications::CommunicationConfiguration.new(
            response: user_response,
            conversation: reviewer_conversation,
            recipient: reviewer,
            correlation_id: correlation_id,
            template_type: "invite_reviewer_skipped"
          )
        )
      end
    end

    it "does not attempt to notify a reviewer if its not present" do
      allow(user_response).to receive(:reviewer) { nil }
      notifications.process_created(process, correlation_id)
      expect(send_communications.delivered.size).to eq(1) # we always invite the user
    end
  end

  describe "reviewer is updated" do
    let(:admin) { instance_double(Person, email: "<EMAIL>") }
    let(:reviewer) { instance_double(Person, name: "Reviewer", display_name: "Reviewer display name", select_preferred_locale: "en") }
    let(:old_reviewer) { instance_double(Person, name: "Old Reviewer", display_name: "Old Reviewer display name", select_preferred_locale: "en") }

    it "invites the new reviewer" do
      notifications.reviewer_updated(process, old_reviewer, admin)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: reviewer_conversation,
          recipient: reviewer,
          template_type: "switch_reviewer"
        )
      )
    end

    it "notifies the removed reviewer" do
      notifications.reviewer_updated(process, old_reviewer, admin)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: reviewer_conversation,
          recipient: old_reviewer,
          template_type: "switch_reviewer_previous"
        )
      )
    end
  end

  describe "response resent" do
    it "reinvites the user" do
      notifications.response_resent(user_response)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: user_conversation,
          recipient: user,
          template_type: "invite_user"
        )
      )
    end
  end

  describe "response reset" do
    it "reinvites the user" do
      notifications.response_resent(user_response)

      expect(send_communications.delivered).to include(
        Communications::CommunicationConfiguration.new(
          response: user_response,
          conversation: user_conversation,
          recipient: user,
          template_type: "invite_user"
        )
      )
    end
  end
end
