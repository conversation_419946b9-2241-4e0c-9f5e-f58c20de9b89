require "rails_helper"

RSpec.describe Props::FilterProps do
  let(:filter_props) do
    Props::FilterProps.from_context(context, date_range, paths)
  end

  let(:account) { FactoryBot.create(:account_without_users) }
  let(:survey) { FactoryBot.create(:basic_survey, account: account, type: :onboard, survey_to_questions: [employee_hierarchy_stq]) }
  let!(:question_1) { FactoryBot.create(:question, name: "Question 1") }
  let!(:survey_to_question_1) { FactoryBot.create(:survey_to_question, question: question_1, survey: survey, type: :culture) }
  let!(:question_2) { FactoryBot.create(:question, name: "Question 2") }
  let!(:survey_to_question_2) { FactoryBot.create(:survey_to_question, question: question_2, survey: survey, type: :culture) }

  let(:filter_1) { double("Filter", survey_to_question: survey_to_question_1, to_s: "filter 1", label: "filter 1") }
  let(:filter_2) { double("Filter", survey_to_question: survey_to_question_2, to_s: "filter 2", label: "filter 2") }
  let(:anchor) { double("Anchor", to_s: "anchor", display_name: "anchor name", hierarchy?: false, option: nil, survey_to_question: nil) }
  let(:anchors) { [] }
  let(:segment_hierarchy) { SegmentHierarchy.create! }
  let!(:question_3) { FactoryBot.create(:question, name: "Question 3") }
  let(:hierarchy_question) { FactoryBot.create(:survey_to_question, question: question_3, segment_hierarchy: segment_hierarchy, survey: survey) }
  let(:employee_hierarchy_question) { FactoryBot.create(:segment_question, name: "Department", hierarchy_type: :employee, select_options: []) }
  let(:employee_hierarchy_stq) { SurveyToQuestion.new(question: employee_hierarchy_question, type: employee_hierarchy_question.type) }
  let(:director) { FactoryBot.create(:user, account: survey.account, name: "Miss. Director") }
  let(:manager1) { FactoryBot.create(:user, account: survey.account, name: "Mr. Manager1") }
  let(:director_select_option) { FactoryBot.create(:select_option, select_question: employee_hierarchy_question, employee_aggregate_id: director.aggregate_id, short_label: director.name) }
  let(:manager1_select_option) { FactoryBot.create(:select_option, select_question: employee_hierarchy_question, employee_aggregate_id: manager1.aggregate_id, short_label: manager1.name) }
  let!(:employee_hierarchy) do
    survey.employee_hierarchies.create!(
      hierarchy_question: employee_hierarchy_question,
      select_option_to_parent: {
        manager1_select_option.id.to_s => director_select_option.id.to_s
      },
      levels: {
        "0" => [director_select_option.id.to_s],
        "1" => [manager1_select_option.id.to_s]
      }
    )
  end
  let(:leader_parameter) { "#{employee_hierarchy_question.id}-#{director_select_option.id}" }
  let(:leader_filter) {
    ReportFilter.new(employee_hierarchy_stq, director_select_option, anchor: false)
  }

  let(:context) do
    instance_double(
      Context,
      account: account,
      filters: selected_filters,
      survey: survey,
      dissect_questions: dissect_questions,
      significant_population: 5,
      anchor: anchor,
      anchors: anchors,
      hierarchy_question: hierarchy_question,
      leader_parameter: leader_parameter,
      leader_filter: leader_filter
    )
  end

  let(:selected_filters) { [] } # none selected by default
  let(:dissect_questions) { [] } # none by default
  let(:responses) { double("responses", submitted: [1, 2, 3, 4]) }
  let(:max_filters) { 5 }
  let(:paths) { double("Paths", filter_path: "", filter_count: "filter count url") }
  let(:is_lifecycle) { false }
  let(:is_adhoc) { false }
  let(:from_date) { nil }
  let(:to_date) { nil }
  let(:default_from) { nil }
  let(:default_to) { nil }

  let(:date_range) do
    instance_double(
      ReportDateRange,
      from: from_date,
      to: to_date,
      default_from: default_from,
      default_to: default_to
    )
  end

  before do
    Timecop.freeze(Date.new(2015, 1, 4))
    employee_hierarchy_stq.update_attributes(employee_hierarchy_id: employee_hierarchy.id)
  end
  after { Timecop.return }

  subject(:props) { filter_props.to_hash }
  subject(:survey_reporting_api_date_filter) { filter_props.survey_reporting_api_date_filter }

  it "includes the max allowed filters" do
    survey.assign_config(Configs::MAXIMUM_REPORT_FILTERS, 5)
    expect(props["maxFilters"]).to eq(5)
  end

  it "includes the url for the filters path" do
    expect(props["filtersUrl"]).not_to be_nil
  end

  it "includes the survey period type" do
    expect(props["surveyPeriodType"]).to eq :snapshot
  end

  it "retrieves the anchor value" do
    expect(props["anchor"][:id]).to eq "anchor"
  end

  it "retrieves the anchor name" do
    expect(props["anchor"][:text]).to eq "anchor name"
  end

  context "from and to date equal default" do
    it "does not include a dateRange" do
      expect(props["dateSummary"][:dateRange]).to be_nil
    end

    it "includes a date summary with Current Results" do
      expect(props["dateSummary"][:timeRange]).to eq "Current Results"
    end
  end

  context "from and to date are different" do
    let(:from_date) { DateTime.new(2016, 1, 20) }
    let(:to_date) { DateTime.new(2016, 3, 20) }
    let(:default_from) { DateTime.new(2016, 1, 10) }
    let(:default_to) { DateTime.new(2016, 4, 20) }

    it "includes the filtered dates in the date summary" do
      expect(props["dateSummary"][:dateRange]).to eq "20 Jan 2016 to 20 Mar 2016"
    end
  end

  context "with hierarchy question" do
    let(:hierarchy_question) { question_1 }
    let(:dissect_questions) { [survey_to_question_1, survey_to_question_2] }

    it "sets hierarchy to true" do
      expect(props["hierarchy"]).to eq true
    end

    it "does not include the hierarchy question in the filters" do
      expect(props["demographics"]).to eq [{id: survey_to_question_2.id.to_s, name: question_2.name, filterModule: nil}]
    end
  end

  context "no hierarchy question" do
    let(:hierarchy_question) { nil }

    it "sets hierarchy to false" do
      expect(props["hierarchy"]).to eq false
    end
  end

  context "non-lifecycle reports" do
    before { survey.type = :engagement }

    it "sets no date filters" do
      expect(props["dateFilters"]).to be_nil
    end

    it "returns no filters for survey reporting api" do
      expect(survey_reporting_api_date_filter).to be_nil
    end
  end

  context "adhoc reports" do
    let(:is_adhoc) { true }

    it "sets date filters" do
      allow(survey).to receive(:flag).and_return(Flags::ENABLED)
      expect(props["dateFilters"]).not_to be_nil
    end

    it "returns no filters for survey reporting api" do
      expect(survey_reporting_api_date_filter).not_to be_nil
    end
  end

  context "lifecycle reports" do
    let(:is_lifecycle) { true }

    it "sets date filters" do
      expect(props["dateFilters"]).not_to be_nil
      expect(survey_reporting_api_date_filter).not_to be_nil
    end

    it "sets the from date from the context" do
      expect(props["dateFilters"][:from_date]).to eq from_date
    end

    it "sets the to date from the context" do
      expect(props["dateFilters"][:to_date]).to eq to_date
    end

    it "sets the max date from the account" do
      expect(props["dateFilters"][:max_date]).to eq "04 Jan 2015"
      expect(survey_reporting_api_date_filter[:maxDate]).to eq "04 Jan 2015"
    end

    context "with dates set" do
      let(:from_date) { Date.new(2014, 10, 19) }
      let(:to_date) { Date.new(2014, 11, 20) }
      let(:default_from) { Date.new(2013, 10, 19) }
      let(:default_to) { Date.new(2015, 11, 20) }

      it "formats the from date" do
        expect(props["dateFilters"][:from_date]).to eq "19 Oct 2014"
      end

      it "formats the to date" do
        expect(props["dateFilters"][:to_date]).to eq "20 Nov 2014"
      end

      it "formats the default from date" do
        expect(props["dateFilters"][:default_from_date]).to eq "19 Oct 2013"
        expect(survey_reporting_api_date_filter[:defaultFromDate]).to eq "19 Oct 2013"
      end

      it "formats the default to date" do
        expect(props["dateFilters"][:default_to_date]).to eq "20 Nov 2015"
        expect(survey_reporting_api_date_filter[:defaultToDate]).to eq "20 Nov 2015"
      end
    end
  end

  context "no filters selected" do
    let(:selected_filters) { [] }

    it "contains no filters" do
      expect(props["demographicFilters"]).to eq []
    end
  end

  context "single filter selected" do
    let(:selected_filters) { [filter_1] }
    let(:expected_filters) do
      [
        {
          demographic_id: survey_to_question_1.id.to_s,
          demographic_label: question_1.name,
          filterModule: nil,
          group_id: "filter 1",
          group_label: "filter 1"
        }
      ]
    end

    it "returns one filter" do
      expect(props["demographicFilters"]).to eq expected_filters
    end
  end

  context "multiple filters selected" do
    let(:selected_filters) { [filter_1, filter_2] }
    let(:expected_filters) do
      [
        {
          demographic_id: survey_to_question_1.id.to_s,
          demographic_label: question_1.name,
          filterModule: nil,
          group_id: "filter 1",
          group_label: "filter 1"
        },
        {
          demographic_id: survey_to_question_2.id.to_s,
          demographic_label: question_2.name,
          filterModule: nil,
          group_id: "filter 2",
          group_label: "filter 2"
        }
      ]
    end

    it "returns two filters" do
      expect(props["demographicFilters"]).to eq expected_filters
    end
  end

  context "when no demographic filters are available" do
    let(:dissect_questions) { [] }

    it "contains no demographics" do
      expect(props["demographics"]).to eq []
    end
  end

  context "when a demographic filter is available" do
    let(:dissect_questions) { [survey_to_question_1] }

    it "contains a hash of the demographic" do
      expect(props["demographics"]).to eq [{id: survey_to_question_1.id.to_s, name: question_1.name, filterModule: nil}]
    end
  end

  context "when multiple demographic filters are available" do
    let(:dissect_questions) { [survey_to_question_1, survey_to_question_2] }
    let(:expected_demographics) do
      [
        {id: survey_to_question_1.id.to_s, name: question_1.name, filterModule: nil},
        {id: survey_to_question_2.id.to_s, name: question_2.name, filterModule: nil}
      ]
    end

    it "contains a hash of the demographics sorted by question name" do
      expect(props["demographics"]).to eq expected_demographics
    end
  end

  context "selected leader" do
    let(:expected_selected_leader) {
      {id: leader_parameter, level: 0}
    }

    it "contains the selected leader hash" do
      expect(props["selectedLeader"]).to eq expected_selected_leader
    end
  end

  context "with anchors" do
    let(:anchor1) { double("Anchor1", to_s: "anchor1", display_name: "anchor name 1", hierarchy?: false, option: nil, survey_to_question: nil) }
    let(:anchor2) { double("Anchor2", to_s: "anchor2", display_name: "anchor name 2", hierarchy?: false, option: nil, survey_to_question: nil) }
    let(:anchors) { [anchor1, anchor2] }

    it "retrieves the anchors array" do
      expect(props["anchors"].length).to eq 2
    end

    it "retrieves each anchor's id" do
      expect(props["anchors"][0][:id]).to eq "anchor1"
      expect(props["anchors"][1][:id]).to eq "anchor2"
    end

    it "retrieves each anchor's text" do
      expect(props["anchors"][0][:text]).to eq "anchor name 1"
      expect(props["anchors"][1][:text]).to eq "anchor name 2"
    end

    context "with hierarchical anchor" do
      let(:hierarchical_anchor) { double("HierarchicalAnchor", to_s: "hierarchical_anchor", display_name: "Hierarchy anchor", hierarchy?: true, option: nil, survey_to_question: nil) }
      let(:anchors) { [hierarchical_anchor] }

      it "sets the hierarchy flag correctly" do
        expect(props["anchors"][0][:hierarchy]).to be true
      end
    end
  end

  context "with empty anchors array" do
    let(:anchors) { [] }

    it "returns an empty anchors array" do
      expect(props["anchors"]).to be_empty
    end
  end
end
