require "rails_helper"

RSpec.describe Reports<PERSON>el<PERSON> do
  describe "#filter_props" do
    let(:filter_1) { double("Filter", survey_to_question: survey_to_question_1, to_s: "filter 1", label: "filter 1") }
    let(:filter_2) { double("Filter", survey_to_question: survey_to_question_2, to_s: "filter 2", label: "filter 2") }
    let(:anchor) { double("Anchor", to_s: "anchor", display_name: "anchor name", hierarchy?: false, option: nil, survey_to_question: nil) }
    let(:from_date) { nil }
    let(:to_date) { nil }
    let(:default_from_date) { nil }
    let(:default_to_date) { nil }
    let(:report_date_range) do
      instance_double(
        ReportDateRange,
        from: from_date,
        to: to_date,
        default_from: default_from_date,
        default_to: default_to_date
      )
    end

    let(:report_context) do
      instance_double(
        Context,
        filters: selected_filters,
        survey: survey,
        dissect_questions: dissect_questions,
        significant_population: 5,
        comment_significant_population: 5,
        anchor: anchor,
        anchors: [],
        account: account,
        hierarchy_question: nil,
        leader_parameter: nil,
        leader_filter: nil
      )
    end
    let(:selected_filters) { [] } # none selected by default
    let(:dissect_questions) { [] } # none by default
    let(:responses) { double("responses", submitted: [1, 2, 3, 4]) }
    let(:account) { FactoryBot.create(:account_without_users) }
    let(:survey) { FactoryBot.create(:basic_survey, account: account, type: :onboard) }
    let!(:question_1) { FactoryBot.create(:question, name: "Question 1") }
    let!(:survey_to_question_1) { FactoryBot.create(:survey_to_question, question: question_1, survey: survey, type: :segment) }
    let!(:question_2) { FactoryBot.create(:question, name: "Question 2") }
    let!(:survey_to_question_2) { FactoryBot.create(:survey_to_question, question: question_2, survey: survey, type: :segment) }

    let(:max_filters) { 5 }
    let(:paths) { double("Paths", filter_path: "", filter_count: "filter count url") }
    let(:participation_result) { Monad::Result.ok(participation_data) }
    let(:participation_data) do
      instance_double(
        Reporting::Data::Participation::Result,
        filtered_result: instance_double(Reporting::Data::Participation::Point, invitation_count: 20, submission_count: 10),
        overall_result: instance_double(Reporting::Data::Participation::Point, invitation_count: 20, submission_count: 10)
      )
    end
    let(:is_lifecycle) { false }
    let(:is_adhoc) { false }

    subject(:props) { helper.filter_props(report_context, report_date_range, paths, participation_result) }

    before { Timecop.freeze(Date.new(2015, 1, 4)) }
    after { Timecop.return }

    it "includes the max allowed filters" do
      expect(props["maxFilters"]).to eq max_filters
    end

    it "includes the url for the filters path" do
      expect(props["filtersUrl"]).not_to be_nil
    end

    it "retrieves the total number of responses" do
      expect(props["responses"][:total_responses]).to eq 10
    end

    it "retrieves the filtered number of responses" do
      expect(props["responses"][:count]).to eq 10
    end

    it "retrieves the total population size" do
      expect(props["responses"][:significant_population]).to eq 5
    end

    it "retrieves the anchor" do
      expect(props["anchor"][:text]).to eq "anchor name"
    end

    context "non-lifecycle reports" do
      before { survey.type = :engagement }

      it "sets no date filters" do
        expect(props["dateFilters"]).to be_nil
      end
    end

    context "adhoc reports" do
      let(:is_adhoc) { true }

      it "sets date filters" do
        allow(survey).to receive(:flag).and_return(Flags::ENABLED)
        expect(props["dateFilters"]).not_to be_nil
      end
    end

    context "lifecycle reports" do
      let(:is_lifecycle) { true }

      it "sets date filters" do
        expect(props["dateFilters"]).not_to be_nil
      end

      it "sets the from date from the context" do
        expect(props["dateFilters"][:from_date]).to eq from_date
      end

      it "sets the to date from the context" do
        expect(props["dateFilters"][:to_date]).to eq to_date
      end

      it "sets the max date from the account" do
        expect(props["dateFilters"][:max_date]).to eq "04 Jan 2015"
      end

      context "with dates set" do
        let(:from_date) { Date.new(2014, 10, 19) }
        let(:to_date) { Date.new(2014, 11, 20) }
        let(:default_from_date) { Date.new(2013, 10, 19) }
        let(:default_to_date) { Date.new(2015, 11, 20) }

        it "formats the from date" do
          expect(props["dateFilters"][:from_date]).to eq "19 Oct 2014"
        end

        it "formats the to date" do
          expect(props["dateFilters"][:to_date]).to eq "20 Nov 2014"
        end

        it "formats the default from date" do
          expect(props["dateFilters"][:default_from_date]).to eq "19 Oct 2013"
        end

        it "formats the default to date" do
          expect(props["dateFilters"][:default_to_date]).to eq "20 Nov 2015"
        end
      end
    end

    context "no filters selected" do
      let(:selected_filters) { [] }

      it "contains no filters" do
        expect(props["demographicFilters"]).to eq []
      end
    end

    context "single filter selected" do
      let(:selected_filters) { [filter_1] }
      let(:expected_filters) do
        [
          {
            demographic_id: survey_to_question_1.id.to_s,
            demographic_label: question_1.name,
            filterModule: nil,
            group_id: "filter 1",
            group_label: "filter 1"
          }
        ]
      end

      it "contains a hash of the stq ID and group" do
        expect(props["demographicFilters"]).to eq(expected_filters)
      end
    end

    context "multiple filters selected" do
      let(:selected_filters) { [filter_1, filter_2] }
      let(:expected_filters) do
        [
          {
            demographic_id: survey_to_question_1.id.to_s,
            demographic_label: question_1.name,
            filterModule: nil,
            group_id: "filter 1",
            group_label: "filter 1"
          },
          {
            demographic_id: survey_to_question_2.id.to_s,
            demographic_label: question_2.name,
            filterModule: nil,
            group_id: "filter 2",
            group_label: "filter 2"
          }
        ]
      end

      it "contains a hash of the stq ID and group for each filter" do
        expect(props["demographicFilters"]).to eq expected_filters
      end
    end

    context "when no demographic filters are available" do
      let(:dissect_questions) { [] }

      it "contains no demographics" do
        expect(props["demographics"]).to eq []
      end
    end

    context "when a demographic filter is available" do
      let(:dissect_questions) { [survey_to_question_1] }

      it "contains a hash of the demographic" do
        expect(props["demographics"]).to eq [{
          id: survey_to_question_1.id.to_s, name: question_1.name, filterModule: nil
        }]
      end
    end

    context "when multiple demographic filters are available" do
      let(:dissect_questions) { [survey_to_question_1, survey_to_question_2] }
      let(:expected_demographics) do
        [
          {id: survey_to_question_1.id.to_s, name: question_1.name, filterModule: nil},
          {id: survey_to_question_2.id.to_s, name: question_2.name, filterModule: nil}
        ]
      end

      it "contains a hash of the demographics sorted by question name" do
        expect(props["demographics"]).to eq expected_demographics
      end
    end
  end
end
