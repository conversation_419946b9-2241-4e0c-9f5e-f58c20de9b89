require "rails_helper"

class TestHelperController < ApplicationController
  include LifecycleFiltersHelper
  attr_accessor :context, :params, :report_date_range
  attr_writer :count_survey_exit_responses_path, :survey_exit_responses_path

  def count_survey_exit_responses_path(*_args)
    @count_survey_exit_responses_path
  end

  def survey_exit_responses_path(*_args)
    @survey_exit_responses_path
  end
end

RSpec.describe LifecycleFiltersHelper do
  describe "#status_options" do
    subject(:status_options_list) { helper.status_options(survey) }

    let(:account) { FactoryBot.build_stubbed(:account) }
    let(:survey) { FactoryBot.build_stubbed(:rich_survey, account: account, questions: survey_questions) }
    let(:workflow) { Configs::LIFECYCLE_WORKFLOW_BASIC }
    let(:lifecycle_close_step) { false }
    let(:survey_questions) { [engagement_question, segment_question] }
    let(:engagement_question) { FactoryBot.build_stubbed(:engagement_question, account: account) }
    let(:segment_question) { FactoryBot.build_stubbed(:segment_question, account: account) }

    before do
      allow(survey).to receive(:three_sixty?) { false }
    end

    it { is_expected.to include(value: "all", label: "All") }
    it { is_expected.to include(value: "issued", label: "Awaiting Response") }
    it { is_expected.to include(value: "closed", label: "Completed") }

    context "full workflow survey" do
      before do
        allow(survey).to receive(:full_workflow?) { true }
      end

      it { is_expected.to include(value: "submitted", label: "Awaiting Interview") }
      it { is_expected.not_to include(value: "interviewed", label: "Awaiting Classification") }
    end

    context "basic workflow survey" do
      before do
        allow(survey).to receive(:full_workflow?) { false }
      end

      it { is_expected.not_to include(value: "submitted", label: "Awaiting Interview") }
      it { is_expected.not_to include(value: "interviewed", label: "Awaiting Classification") }
    end

    context "survey has classification questions" do
      before do
        allow(survey).to receive(:full_workflow?) { false }
        allow(survey).to receive(:lifecycle_close_step?) { true }
      end

      it { is_expected.not_to include(value: "submitted", label: "Awaiting Interview") }
      it { is_expected.to include(value: "interviewed", label: "Awaiting Classification") }
    end

    context "survey has lifecycle close step" do
      before do
        allow(survey).to receive(:lifecycle_close_step?).and_return(true)
      end

      it { is_expected.to include(value: "interviewed", label: "Awaiting Classification") }
    end

    context "three sixty survey" do
      before do
        allow(survey).to receive(:three_sixty?) { true }
      end

      it { is_expected.to include(value: "nominate", label: "Nominate") }
      it { is_expected.to include(value: "ready", label: "Ready") }
      it { is_expected.to include(value: "issued", label: "Collect") }
      it { is_expected.to include(value: "submitted", label: "Coach") }
      it { is_expected.to include(value: "shared", label: "Review") }
      it { is_expected.to include(value: "closed", label: "Complete") }

      context "coachless" do
        before do
          allow(survey).to receive(:full_workflow?).and_return(false)
        end

        it { is_expected.not_to include(value: "submitted", label: "Coach") }
      end
    end
  end

  describe "#exit_response_filter_props" do
    let(:max_filters) { 10 }
    let(:count_path) { "count path" }
    let(:exit_responses_path) { "exit responses path" }
    let(:survey) do
      instance_double(
        Survey,
        active_demographics: [stq],
        lifecycle?: true,
        full_workflow?: false,
        lifecycle_close_step?: false
      )
    end

    let(:filter_props) { instance_double(Props::FilterProps, to_hash: filter_props_hash) }

    let(:filter_props_hash) do
      {
        "demographicFilters" => ["demographic_filter"],
        "maxFilters" => max_filters
      }
    end

    let(:question) do
      question = SingleSelectQuestion.new
      question.select_options.new(id: 10, short_label: "option 1", sort_term: "sort term 1")
      question
    end
    let(:stq) { instance_double(SurveyToQuestion, id: 10, question: question, _question: question) }
    let(:select_option) { question.select_options.first }
    let(:filter) { double("Filter", survey_to_question: stq, label: "filter") }
    let(:report_date_range) { instance_double(ReportDateRange) }
    let(:account) { instance_double(Account, time_zone: ActiveSupport::TimeZone["Australia/Melbourne"], status: :active) }
    let(:survey_navigation) { instance_double(Navigation::LifecyclePaths, filter: "filter_path") }
    let(:user) { instance_double(Person, superuser?: false) }
    let(:context) do
      instance_double(
        Context,
        significant_population: 1,
        filters: [filter],
        survey: survey,
        dissect_questions: [],
        anchor: nil,
        anchors: [],
        account: account,
        hierarchy_question: nil,
        user: user,
        leader_parameter: nil,
        leader_filter: nil
      )
    end
    let(:selected_status) { "issued" }
    let(:filtered_responses) { [] }
    let(:all_responses) { [] }
    let(:helper) do
      test_helper = TestHelperController.new
      test_helper.context = context
      test_helper.count_survey_exit_responses_path = count_path
      test_helper.survey_exit_responses_path = exit_responses_path
      test_helper.params = {status: selected_status}
      test_helper.report_date_range = report_date_range
      test_helper
    end

    let(:report_select_query) { instance_double(ReportSelectQuery, select_options_for_stq_in_responses: select_options_in_responses) }
    let(:select_options_in_responses) { [] }

    subject { helper.exit_response_filter_props(filtered_responses, all_responses) }

    before do
      allow(Navigation::LifecyclePaths).to receive(:new).and_return(survey_navigation)
      allow(ReportSelectQuery).to receive(:new).and_return(report_select_query)
      allow(Props::FilterProps).to receive(:new).and_return(filter_props)
      allow(survey).to receive(:three_sixty?) { false }
    end

    context "when there are no respondants in the deleted demographic option" do
      it "includes the demographic groups" do
        expect(subject["demographicGroups"]).to be_a Hash
        expect(subject["demographicGroups"].size).to be 1
      end
    end

    context "when there are respondants in the deleted demographic option" do
      let(:select_options_in_responses) { [deleted_select_option] }
      let(:deleted_select_option) { instance_double(SelectOption, label: "label", id: "id") }
      let(:expected_demographic_group_hash) do
        {
          stq.id => select_options_hash
        }
      end
      let(:select_options_hash) do
        [
          {
            id: "#{stq.id}-#{deleted_select_option.id}",
            text: deleted_select_option.label,
            text_without_count: deleted_select_option.label
          },
          {
            id: "#{stq.id}-#{select_option.id}",
            text: select_option.label,
            text_without_count: select_option.label
          }
        ]
      end
      it "includes the demographic groups" do
        expect(subject["demographicGroups"]).to eql(expected_demographic_group_hash)
      end
    end

    it "includes the selected filters" do
      expect(subject["demographicFilters"].size).to be 1
    end

    it "includes the max filters" do
      expect(subject["maxFilters"]).to eq max_filters
    end

    it "includes the demographics" do
      expect(subject["demographics"].size).to eq 1
    end

    it "includes the filter count url" do
      expect(subject["filterCountUrl"]).to eq count_path
    end

    it "includes responses" do
      expect(subject["responses"]).to be_a Hash
    end

    it "includes the results url" do
      expect(subject["resultsUrl"]).to eq "filter_path"
    end

    it "includes status options" do
      expect(subject["statusOptions"]).to be_a Array
    end

    it "includes the selected status" do
      expect(subject["selectedStatus"]).to eq selected_status
    end
  end

  describe "#response_count" do
    let(:filtered_responses) { [1, 2, 3] }
    let(:all_responses) { [1, 2, 3, 4, 5, 6, 7] }
    let(:responses) do
      test_helper = TestHelperController.new
      test_helper.context = context
      test_helper.response_count(filtered_responses, all_responses)
    end
    let(:context) { instance_double(Context, significant_population: 1) }

    it "includes the count of filtered responses" do
      expect(responses[:count]).to eq 3
    end

    it "includes the count of total responses" do
      expect(responses[:total_responses]).to eq 7
    end

    it "includes the significant population" do
      expect(responses[:significant_population]).to eq 1
    end
  end
end
